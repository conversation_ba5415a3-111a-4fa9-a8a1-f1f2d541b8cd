<?php if (!defined('__TYPECHO_ROOT_DIR__')) exit; ?>
<?php $this->need('header.php'); ?>

<main class="main-content">
    <div class="container">
        <div class="error-page">
            <div class="error-content">
                <!-- 404 Illustration -->
                <div class="error-illustration">
                    <div class="error-number">
                        <span class="digit">4</span>
                        <span class="digit">0</span>
                        <span class="digit">4</span>
                    </div>
                    <div class="error-icon">
                        <svg viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <!-- Broken page icon -->
                            <rect x="40" y="30" width="120" height="140" rx="8" stroke="currentColor" stroke-width="3" fill="none"/>
                            <path d="M40 60 L160 60" stroke="currentColor" stroke-width="2"/>
                            <path d="M50 80 L110 80" stroke="currentColor" stroke-width="2"/>
                            <path d="M50 100 L130 100" stroke="currentColor" stroke-width="2"/>
                            <path d="M50 120 L90 120" stroke="currentColor" stroke-width="2"/>
                            
                            <!-- Crack effect -->
                            <path d="M120 80 L140 120 L160 100 L180 140" stroke="currentColor" stroke-width="3" fill="none" stroke-linecap="round"/>
                            <circle cx="140" cy="120" r="3" fill="currentColor"/>
                            <circle cx="160" cy="100" r="2" fill="currentColor"/>
                            
                            <!-- Floating elements -->
                            <circle cx="30" cy="50" r="4" fill="currentColor" opacity="0.3">
                                <animate attributeName="cy" values="50;40;50" dur="3s" repeatCount="indefinite"/>
                            </circle>
                            <circle cx="180" cy="80" r="3" fill="currentColor" opacity="0.4">
                                <animate attributeName="cy" values="80;70;80" dur="2.5s" repeatCount="indefinite"/>
                            </circle>
                            <circle cx="20" cy="120" r="2" fill="currentColor" opacity="0.5">
                                <animate attributeName="cy" values="120;110;120" dur="4s" repeatCount="indefinite"/>
                            </circle>
                        </svg>
                    </div>
                </div>

                <!-- Error Message -->
                <div class="error-message">
                    <h1 class="error-title">页面未找到</h1>
                    <p class="error-description">
                        抱歉，您访问的页面不存在或已被移动。<br>
                        可能是链接错误或页面已被删除。
                    </p>
                </div>

                <!-- Search Box -->
                <div class="error-search">
                    <h3 class="search-title">尝试搜索您需要的内容</h3>
                    <form class="search-form" method="post" action="<?php $this->options->siteUrl(); ?>" role="search">
                        <div class="search-input-group">
                            <input type="search" 
                                   name="s" 
                                   class="search-input" 
                                   placeholder="输入关键词搜索..." 
                                   required
                                   autocomplete="off">
                            <button type="submit" class="search-submit" aria-label="搜索">
                                <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <circle cx="11" cy="11" r="8"></circle>
                                    <path d="M21 21l-4.35-4.35"></path>
                                </svg>
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Quick Links -->
                <div class="error-links">
                    <h3 class="links-title">或者访问以下页面</h3>
                    <div class="links-grid">
                        <a href="<?php $this->options->siteUrl(); ?>" class="link-card">
                            <div class="link-icon">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                                    <polyline points="9,22 9,12 15,12 15,22"></polyline>
                                </svg>
                            </div>
                            <div class="link-content">
                                <h4 class="link-title">返回首页</h4>
                                <p class="link-desc">浏览最新文章和内容</p>
                            </div>
                        </a>

                        <a href="<?php $this->options->siteUrl(); ?>archives.html" class="link-card">
                            <div class="link-icon">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                    <polyline points="14,2 14,8 20,8"></polyline>
                                    <line x1="16" y1="13" x2="8" y2="13"></line>
                                    <line x1="16" y1="17" x2="8" y2="17"></line>
                                </svg>
                            </div>
                            <div class="link-content">
                                <h4 class="link-title">文章归档</h4>
                                <p class="link-desc">查看所有历史文章</p>
                            </div>
                        </a>

                        <?php 
                        $categories = $this->widget('Widget_Metas_Category_List');
                        if ($categories->have()): 
                            $categories->next();
                        ?>
                        <a href="<?php $categories->permalink(); ?>" class="link-card">
                            <div class="link-icon">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path>
                                </svg>
                            </div>
                            <div class="link-content">
                                <h4 class="link-title">文章分类</h4>
                                <p class="link-desc">按分类浏览文章</p>
                            </div>
                        </a>
                        <?php endif; ?>

                        <a href="<?php $this->options->siteUrl(); ?>about.html" class="link-card">
                            <div class="link-icon">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                    <circle cx="12" cy="7" r="4"></circle>
                                </svg>
                            </div>
                            <div class="link-content">
                                <h4 class="link-title">关于我们</h4>
                                <p class="link-desc">了解更多信息</p>
                            </div>
                        </a>
                    </div>
                </div>

                <!-- Recent Posts -->
                <div class="error-recent">
                    <h3 class="recent-title">最新文章</h3>
                    <div class="recent-posts-list">
                        <?php 
                        $recentPosts = $this->widget('Widget_Contents_Post_Recent', 'pageSize=6');
                        while ($recentPosts->next()): 
                        ?>
                        <article class="recent-post-item">
                            <?php if ($recentPosts->fields->thumbnail || has_post_thumbnail($recentPosts)): ?>
                                <div class="recent-post-thumbnail">
                                    <a href="<?php $recentPosts->permalink() ?>" title="<?php $recentPosts->title() ?>">
                                        <img src="<?php echo get_post_thumbnail($recentPosts); ?>" 
                                             alt="<?php $recentPosts->title() ?>" 
                                             loading="lazy">
                                    </a>
                                </div>
                            <?php endif; ?>
                            <div class="recent-post-content">
                                <h4 class="recent-post-title">
                                    <a href="<?php $recentPosts->permalink() ?>" title="<?php $recentPosts->title() ?>">
                                        <?php $recentPosts->title() ?>
                                    </a>
                                </h4>
                                <div class="recent-post-meta">
                                    <time datetime="<?php $recentPosts->date('c'); ?>">
                                        <?php $recentPosts->date('Y-m-d'); ?>
                                    </time>
                                    <span class="meta-separator">·</span>
                                    <span class="post-category">
                                        <?php $recentPosts->category(',', false); ?>
                                    </span>
                                </div>
                            </div>
                        </article>
                        <?php endwhile; ?>
                    </div>
                </div>

                <!-- Back Button -->
                <div class="error-actions">
                    <button onclick="history.back()" class="btn btn-secondary">
                        <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <polyline points="15,18 9,12 15,6"></polyline>
                        </svg>
                        返回上一页
                    </button>
                    <a href="<?php $this->options->siteUrl(); ?>" class="btn btn-primary">
                        <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                            <polyline points="9,22 9,12 15,12 15,22"></polyline>
                        </svg>
                        回到首页
                    </a>
                </div>
            </div>
        </div>
    </div>
</main>

<script>
// Add some interactive effects
document.addEventListener('DOMContentLoaded', function() {
    // Animate the 404 digits
    const digits = document.querySelectorAll('.digit');
    digits.forEach((digit, index) => {
        digit.style.animationDelay = `${index * 0.2}s`;
        digit.classList.add('animate-bounce');
    });
    
    // Add hover effects to link cards
    const linkCards = document.querySelectorAll('.link-card');
    linkCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
});
</script>

<?php $this->need('footer.php'); ?>
