# Robots.txt for Modern Typecho Theme
# Generated automatically

User-agent: *
Allow: /

# Disallow admin and system directories
Disallow: /admin/
Disallow: /usr/
Disallow: /var/
Disallow: /install/
Disallow: /install.php

# Disallow query parameters
Disallow: /*?*
Disallow: /*&*

# Disallow specific file types
Disallow: /*.php$
Disallow: /*.inc$
Disallow: /*.conf$

# Allow specific directories
Allow: /assets/
Allow: /usr/themes/*/assets/

# Crawl delay (optional)
Crawl-delay: 1

# Sitemap location
Sitemap: https://example.com/sitemap.xml
Sitemap: https://example.com/sitemap.php

# Specific rules for different bots

# Google
User-agent: Googlebot
Allow: /
Crawl-delay: 0

# Bing
User-agent: Bingbot
Allow: /
Crawl-delay: 1

# Baidu (Chinese search engine)
User-agent: Baiduspider
Allow: /
Crawl-delay: 1

# Yandex (Russian search engine)
User-agent: Yandex
Allow: /
Crawl-delay: 1

# Block bad bots
User-agent: AhrefsBot
Disallow: /

User-agent: MJ12bot
Disallow: /

User-agent: DotBot
Disallow: /

User-agent: SemrushBot
Disallow: /

User-agent: MegaIndex
Disallow: /

# Block AI training bots (optional)
User-agent: GPTBot
Disallow: /

User-agent: ChatGPT-User
Disallow: /

User-agent: CCBot
Disallow: /

User-agent: anthropic-ai
Disallow: /

User-agent: Claude-Web
Disallow: /
