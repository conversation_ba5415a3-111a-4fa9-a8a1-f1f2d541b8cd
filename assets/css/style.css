/*
 * Modern Responsive Typecho Theme
 * CSS Variables and Design System
 */

/* ===== CSS Variables ===== */
:root {
  /* Colors - Light Theme */
  --color-primary: #2563eb;
  --color-primary-hover: #1d4ed8;
  --color-primary-light: #dbeafe;
  --color-secondary: #64748b;
  --color-accent: #f59e0b;
  
  /* Background Colors */
  --color-bg-primary: #ffffff;
  --color-bg-secondary: #f8fafc;
  --color-bg-tertiary: #f1f5f9;
  --color-bg-overlay: rgba(0, 0, 0, 0.5);
  
  /* Text Colors */
  --color-text-primary: #1e293b;
  --color-text-secondary: #64748b;
  --color-text-tertiary: #94a3b8;
  --color-text-inverse: #ffffff;
  
  /* Border Colors */
  --color-border-primary: #e2e8f0;
  --color-border-secondary: #cbd5e1;
  --color-border-focus: var(--color-primary);
  
  /* Status Colors */
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Typography */
  --font-family-sans: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --font-family-serif: Georgia, Cambria, "Times New Roman", Times, serif;
  --font-family-mono: Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  
  /* Font Sizes */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;
  
  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;
  
  /* Spacing */
  --spacing-0: 0;
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;
  --spacing-20: 5rem;
  --spacing-24: 6rem;
  --spacing-32: 8rem;
  
  /* Border Radius */
  --radius-sm: 0.125rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-full: 9999px;
  
  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
  
  /* Z-Index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  
  /* Container Widths */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;
}

/* Dark Theme Variables */
[data-theme="dark"] {
  /* Background Colors */
  --color-bg-primary: #0f172a;
  --color-bg-secondary: #1e293b;
  --color-bg-tertiary: #334155;
  --color-bg-overlay: rgba(0, 0, 0, 0.8);
  
  /* Text Colors */
  --color-text-primary: #f1f5f9;
  --color-text-secondary: #cbd5e1;
  --color-text-tertiary: #94a3b8;
  --color-text-inverse: #1e293b;
  
  /* Border Colors */
  --color-border-primary: #334155;
  --color-border-secondary: #475569;
}

/* Auto Theme (System Preference) */
@media (prefers-color-scheme: dark) {
  [data-theme="auto"] {
    /* Background Colors */
    --color-bg-primary: #0f172a;
    --color-bg-secondary: #1e293b;
    --color-bg-tertiary: #334155;
    --color-bg-overlay: rgba(0, 0, 0, 0.8);
    
    /* Text Colors */
    --color-text-primary: #f1f5f9;
    --color-text-secondary: #cbd5e1;
    --color-text-tertiary: #94a3b8;
    --color-text-inverse: #1e293b;
    
    /* Border Colors */
    --color-border-primary: #334155;
    --color-border-secondary: #475569;
  }
}

/* ===== Base Styles ===== */
* {
  box-sizing: border-box;
}

*::before,
*::after {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: var(--line-height-normal);
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

body {
  margin: 0;
  font-family: var(--font-family-sans);
  font-size: var(--font-size-base);
  font-weight: 400;
  line-height: var(--line-height-normal);
  color: var(--color-text-primary);
  background-color: var(--color-bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* ===== Typography ===== */
h1, h2, h3, h4, h5, h6 {
  margin: 0 0 var(--spacing-4) 0;
  font-weight: 600;
  line-height: var(--line-height-tight);
  color: var(--color-text-primary);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
  margin: 0 0 var(--spacing-4) 0;
  line-height: var(--line-height-relaxed);
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--color-primary-hover);
}

a:focus {
  outline: 2px solid var(--color-border-focus);
  outline-offset: 2px;
}

/* ===== Layout ===== */
.container {
  width: 100%;
  max-width: var(--container-xl);
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

@media (min-width: 640px) {
  .container {
    padding: 0 var(--spacing-6);
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 var(--spacing-8);
  }
}

.content-wrapper {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-8);
}

@media (min-width: 1024px) {
  .content-wrapper {
    grid-template-columns: 1fr 320px;
  }
}

/* ===== Utilities ===== */
.screen-reader-text {
  position: absolute !important;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  z-index: var(--z-tooltip);
  padding: var(--spacing-2) var(--spacing-4);
  background-color: var(--color-bg-primary);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-md);
  text-decoration: none;
  transition: top var(--transition-fast);
}

.skip-link:focus {
  top: 6px;
}

/* ===== Icons ===== */
.icon {
  width: 1.25rem;
  height: 1.25rem;
  fill: currentColor;
  flex-shrink: 0;
}

/* ===== Buttons ===== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-4);
  font-size: var(--font-size-sm);
  font-weight: 500;
  line-height: 1;
  text-align: center;
  text-decoration: none;
  white-space: nowrap;
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  user-select: none;
}

.btn:focus {
  outline: 2px solid var(--color-border-focus);
  outline-offset: 2px;
}

.btn-primary {
  color: var(--color-text-inverse);
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.btn-primary:hover {
  background-color: var(--color-primary-hover);
  border-color: var(--color-primary-hover);
}

.btn-secondary {
  color: var(--color-text-primary);
  background-color: var(--color-bg-secondary);
  border-color: var(--color-border-primary);
}

.btn-secondary:hover {
  background-color: var(--color-bg-tertiary);
}

/* ===== Forms ===== */
.form-control {
  display: block;
  width: 100%;
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--color-text-primary);
  background-color: var(--color-bg-primary);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-md);
  transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.form-control:focus {
  outline: none;
  border-color: var(--color-border-focus);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-control::placeholder {
  color: var(--color-text-tertiary);
}

/* ===== Animations ===== */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn var(--transition-normal) ease-out;
}

.fade-in-up {
  animation: fadeInUp var(--transition-normal) ease-out;
}

.slide-in-down {
  animation: slideInDown var(--transition-normal) ease-out;
}

/* ===== Responsive Design ===== */
@media (max-width: 767px) {
  :root {
    --font-size-4xl: 2rem;
    --font-size-3xl: 1.5rem;
    --font-size-2xl: 1.25rem;
  }
  
  .container {
    padding: 0 var(--spacing-4);
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* ===== Header Styles ===== */
.site-header {
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  background-color: var(--color-bg-primary);
  border-bottom: 1px solid var(--color-border-primary);
  backdrop-filter: blur(10px);
  transition: all var(--transition-normal);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-4) 0;
}

/* Site Branding */
.site-branding {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.site-logo {
  display: flex;
  align-items: center;
  text-decoration: none;
}

.logo-image {
  height: 40px;
  width: auto;
}

.site-title {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--color-text-primary);
  margin: 0;
}

.site-description {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0;
}

/* Navigation */
.main-navigation {
  display: flex;
  align-items: center;
}

.menu-toggle {
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
}

.hamburger {
  display: flex;
  flex-direction: column;
  width: 20px;
  height: 16px;
}

.hamburger span {
  display: block;
  height: 2px;
  width: 100%;
  background-color: var(--color-text-primary);
  margin-bottom: 3px;
  transition: all var(--transition-fast);
  transform-origin: center;
}

.hamburger span:last-child {
  margin-bottom: 0;
}

.menu-toggle[aria-expanded="true"] .hamburger span:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.menu-toggle[aria-expanded="true"] .hamburger span:nth-child(2) {
  opacity: 0;
}

.menu-toggle[aria-expanded="true"] .hamburger span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-6);
  margin: 0;
  padding: 0;
  list-style: none;
}

.nav-menu a {
  display: block;
  padding: var(--spacing-2) 0;
  font-weight: 500;
  color: var(--color-text-secondary);
  text-decoration: none;
  transition: color var(--transition-fast);
  position: relative;
}

.nav-menu a::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--color-primary);
  transition: width var(--transition-fast);
}

.nav-menu a:hover,
.nav-menu .current-menu-item a {
  color: var(--color-primary);
}

.nav-menu a:hover::after,
.nav-menu .current-menu-item a::after {
  width: 100%;
}

/* Header Actions */
.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.search-toggle,
.theme-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: none;
  border: none;
  border-radius: var(--radius-full);
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.search-toggle:hover,
.theme-toggle:hover {
  background-color: var(--color-bg-secondary);
  color: var(--color-text-primary);
}

.theme-toggle .sun-icon {
  display: block;
}

.theme-toggle .moon-icon {
  display: none;
}

[data-theme="dark"] .theme-toggle .sun-icon {
  display: none;
}

[data-theme="dark"] .theme-toggle .moon-icon {
  display: block;
}

/* Search Overlay */
.search-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: var(--z-modal);
  background-color: var(--color-bg-overlay);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.search-overlay.active {
  opacity: 1;
  visibility: visible;
}

.search-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 600px;
  background-color: var(--color-bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  padding: var(--spacing-6);
}

.search-form {
  position: relative;
  display: flex;
  align-items: center;
}

.search-form input {
  flex: 1;
  padding: var(--spacing-4) var(--spacing-6);
  font-size: var(--font-size-lg);
  border: 2px solid var(--color-border-primary);
  border-radius: var(--radius-lg);
  background-color: var(--color-bg-secondary);
}

.search-submit {
  position: absolute;
  right: var(--spacing-2);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: var(--color-primary);
  color: var(--color-text-inverse);
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.search-submit:hover {
  background-color: var(--color-primary-hover);
}

.search-close {
  position: absolute;
  top: var(--spacing-4);
  right: var(--spacing-4);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  border-radius: var(--radius-full);
  transition: all var(--transition-fast);
}

.search-close:hover {
  background-color: var(--color-bg-secondary);
  color: var(--color-text-primary);
}

/* ===== Main Content ===== */
.main-content {
  padding: var(--spacing-8) 0;
  min-height: calc(100vh - 200px);
}

/* Breadcrumb */
.breadcrumb {
  margin-bottom: var(--spacing-6);
}

.breadcrumb-list {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin: 0;
  padding: 0;
  list-style: none;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.breadcrumb-item:not(:last-child)::after {
  content: '/';
  margin-left: var(--spacing-2);
  color: var(--color-text-tertiary);
}

.breadcrumb-item a {
  color: var(--color-text-secondary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.breadcrumb-item a:hover {
  color: var(--color-primary);
}

/* Posts Section */
.posts-section {
  margin-bottom: var(--spacing-8);
}

.section-title {
  font-size: var(--font-size-2xl);
  font-weight: 600;
  margin-bottom: var(--spacing-6);
  color: var(--color-text-primary);
}

/* Featured Posts */
.featured-posts {
  margin-bottom: var(--spacing-12);
}

.featured-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-6);
}

/* Posts Container */
.posts-container {
  display: grid;
  gap: var(--spacing-8);
}

.posts-container[data-layout="grid"] {
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: var(--spacing-6);
}

.posts-container[data-layout="list"] {
  grid-template-columns: 1fr;
  gap: var(--spacing-8);
}

.posts-container[data-layout="magazine"] {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-6);
}

/* Post Card */
.post-card {
  background-color: var(--color-bg-primary);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
}

.post-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--color-border-secondary);
}

.post-thumbnail {
  position: relative;
  aspect-ratio: 16/9;
  overflow: hidden;
}

.post-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-slow);
}

.post-card:hover .post-thumbnail img {
  transform: scale(1.05);
}

.post-sticky {
  position: absolute;
  top: var(--spacing-3);
  right: var(--spacing-3);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background-color: var(--color-accent);
  color: var(--color-text-inverse);
  border-radius: var(--radius-full);
  box-shadow: var(--shadow-md);
}

.post-content {
  padding: var(--spacing-6);
}

.post-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-4);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.meta-separator {
  color: var(--color-text-tertiary);
}

.post-comments {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.post-title {
  margin-bottom: var(--spacing-3);
  font-size: var(--font-size-xl);
  font-weight: 600;
  line-height: var(--line-height-tight);
}

.post-title a {
  color: var(--color-text-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.post-title a:hover {
  color: var(--color-primary);
}

.post-excerpt {
  margin-bottom: var(--spacing-4);
  color: var(--color-text-secondary);
  line-height: var(--line-height-relaxed);
}

.post-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-4);
}

.post-tags a {
  display: inline-block;
  padding: var(--spacing-1) var(--spacing-3);
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  background-color: var(--color-bg-secondary);
  border-radius: var(--radius-full);
  text-decoration: none;
  transition: all var(--transition-fast);
}

.post-tags a:hover {
  color: var(--color-primary);
  background-color: var(--color-primary-light);
}

.post-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.read-more-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-4);
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--color-primary);
  background-color: var(--color-primary-light);
  border-radius: var(--radius-md);
  text-decoration: none;
  transition: all var(--transition-fast);
}

.read-more-btn:hover {
  background-color: var(--color-primary);
  color: var(--color-text-inverse);
}

/* No Posts */
.no-posts {
  text-align: center;
  padding: var(--spacing-16) var(--spacing-8);
  color: var(--color-text-secondary);
}

.no-posts-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto var(--spacing-6);
  color: var(--color-text-tertiary);
}

.no-posts h2 {
  margin-bottom: var(--spacing-4);
  color: var(--color-text-primary);
}

/* ===== Pagination ===== */
.pagination {
  margin-top: var(--spacing-8);
  text-align: center;
}

.page-navigator {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
  margin: 0;
  padding: 0;
  list-style: none;
}

.page-navigator li {
  display: flex;
}

.page-navigator a,
.page-navigator span {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  height: 40px;
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--color-text-secondary);
  background-color: var(--color-bg-primary);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-md);
  text-decoration: none;
  transition: all var(--transition-fast);
}

.page-navigator a:hover {
  color: var(--color-primary);
  background-color: var(--color-primary-light);
  border-color: var(--color-primary);
}

.page-navigator .current span {
  color: var(--color-text-inverse);
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

/* ===== Sidebar ===== */
.sidebar {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-8);
}

.widget {
  background-color: var(--color-bg-primary);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.widget-title {
  margin: 0;
  padding: var(--spacing-4) var(--spacing-6);
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--color-text-primary);
  background-color: var(--color-bg-secondary);
  border-bottom: 1px solid var(--color-border-primary);
}

.widget-content {
  padding: var(--spacing-6);
}

/* Author Widget */
.author-card {
  text-align: center;
}

.author-avatar {
  margin-bottom: var(--spacing-4);
}

.avatar-image {
  width: 80px;
  height: 80px;
  border-radius: var(--radius-full);
  object-fit: cover;
  border: 3px solid var(--color-border-primary);
}

.author-name {
  margin-bottom: var(--spacing-2);
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--color-text-primary);
}

.author-description {
  margin-bottom: var(--spacing-4);
  color: var(--color-text-secondary);
  line-height: var(--line-height-relaxed);
}

.author-stats {
  display: flex;
  justify-content: center;
  gap: var(--spacing-6);
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--color-primary);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

/* Search Widget */
.search-input-group {
  position: relative;
  display: flex;
}

.search-input-group input {
  flex: 1;
  padding-right: 50px;
}

.search-input-group .search-submit {
  position: absolute;
  right: var(--spacing-1);
  top: 50%;
  transform: translateY(-50%);
  width: 36px;
  height: 36px;
}

/* Recent Posts Widget */
.recent-posts-list {
  margin: 0;
  padding: 0;
  list-style: none;
}

.recent-post-item {
  display: flex;
  gap: var(--spacing-3);
  padding: var(--spacing-3) 0;
  border-bottom: 1px solid var(--color-border-primary);
}

.recent-post-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.recent-post-item:first-child {
  padding-top: 0;
}

.recent-post-thumbnail {
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  border-radius: var(--radius-md);
  overflow: hidden;
}

.recent-post-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.recent-post-content {
  flex: 1;
  min-width: 0;
}

.recent-post-title {
  margin: 0 0 var(--spacing-1) 0;
  font-size: var(--font-size-sm);
  font-weight: 500;
  line-height: var(--line-height-snug);
  color: var(--color-text-primary);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.recent-post-date {
  font-size: var(--font-size-xs);
  color: var(--color-text-tertiary);
}

.recent-post-link {
  text-decoration: none;
  color: inherit;
}

.recent-post-link:hover .recent-post-title {
  color: var(--color-primary);
}

/* Categories Widget */
.categories-list {
  margin: 0;
  padding: 0;
  list-style: none;
}

.category-item {
  border-bottom: 1px solid var(--color-border-primary);
}

.category-item:last-child {
  border-bottom: none;
}

.category-link {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-3) 0;
  color: var(--color-text-secondary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.category-link:hover {
  color: var(--color-primary);
}

.category-count {
  font-size: var(--font-size-xs);
  color: var(--color-text-tertiary);
  background-color: var(--color-bg-secondary);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-full);
  min-width: 20px;
  text-align: center;
}

/* Tags Widget */
.tags-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-2);
}

.tag-link {
  display: inline-block;
  padding: var(--spacing-1) var(--spacing-3);
  color: var(--color-text-secondary);
  background-color: var(--color-bg-secondary);
  border-radius: var(--radius-full);
  text-decoration: none;
  transition: all var(--transition-fast);
  font-weight: 400;
}

.tag-link:hover {
  color: var(--color-primary);
  background-color: var(--color-primary-light);
  transform: translateY(-1px);
}

.no-tags {
  color: var(--color-text-tertiary);
  font-style: italic;
  text-align: center;
  margin: 0;
}

/* Recent Comments Widget */
.recent-comments-list {
  margin: 0;
  padding: 0;
  list-style: none;
}

.recent-comment-item {
  display: flex;
  gap: var(--spacing-3);
  padding: var(--spacing-3) 0;
  border-bottom: 1px solid var(--color-border-primary);
}

.recent-comment-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.recent-comment-item:first-child {
  padding-top: 0;
}

.comment-avatar {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  overflow: hidden;
}

.comment-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.comment-content {
  flex: 1;
  min-width: 0;
}

.comment-author {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-1);
}

.author-name {
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--color-text-primary);
}

.comment-date {
  font-size: var(--font-size-xs);
  color: var(--color-text-tertiary);
}

.comment-text {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  line-height: var(--line-height-snug);
}

.comment-text a {
  color: inherit;
  text-decoration: none;
}

.comment-text a:hover {
  color: var(--color-primary);
}

/* Archives Widget */
.archives-list {
  margin: 0;
  padding: 0;
  list-style: none;
}

.archive-item {
  border-bottom: 1px solid var(--color-border-primary);
}

.archive-item:last-child {
  border-bottom: none;
}

.archive-link {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-3) 0;
  color: var(--color-text-secondary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.archive-link:hover {
  color: var(--color-primary);
}

.archive-count {
  font-size: var(--font-size-xs);
  color: var(--color-text-tertiary);
  background-color: var(--color-bg-secondary);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-full);
  min-width: 20px;
  text-align: center;
}

/* Friends Widget */
.friends-list {
  margin: 0;
  padding: 0;
  list-style: none;
}

.friend-item {
  margin-bottom: var(--spacing-3);
}

.friend-item:last-child {
  margin-bottom: 0;
}

.friend-link {
  display: block;
  padding: var(--spacing-3);
  background-color: var(--color-bg-secondary);
  border-radius: var(--radius-md);
  text-decoration: none;
  transition: all var(--transition-fast);
}

.friend-link:hover {
  background-color: var(--color-bg-tertiary);
  transform: translateY(-1px);
}

.friend-name {
  display: block;
  font-weight: 500;
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-1);
}

.friend-desc {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  line-height: var(--line-height-snug);
}

/* ===== Footer ===== */
.site-footer {
  background-color: var(--color-bg-secondary);
  border-top: 1px solid var(--color-border-primary);
  margin-top: var(--spacing-16);
}

/* Footer Widgets */
.footer-widgets {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-8);
  padding: var(--spacing-12) 0;
  border-bottom: 1px solid var(--color-border-primary);
}

.footer-widget-area h3 {
  margin-bottom: var(--spacing-4);
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--color-text-primary);
}

.footer-links {
  margin: 0;
  padding: 0;
  list-style: none;
}

.footer-links li {
  margin-bottom: var(--spacing-2);
}

.footer-links a {
  color: var(--color-text-secondary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.footer-links a:hover {
  color: var(--color-primary);
}

/* Social Links */
.social-links {
  display: flex;
  gap: var(--spacing-3);
}

.social-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  color: var(--color-text-secondary);
  background-color: var(--color-bg-tertiary);
  border-radius: var(--radius-full);
  text-decoration: none;
  transition: all var(--transition-fast);
}

.social-links a:hover {
  color: var(--color-text-inverse);
  background-color: var(--color-primary);
  transform: translateY(-2px);
}

.wechat-toggle {
  position: relative;
}

.wechat-qr {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-bottom: var(--spacing-2);
  padding: var(--spacing-3);
  background-color: var(--color-bg-primary);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-fast);
}

.wechat-toggle:hover .wechat-qr {
  opacity: 1;
  visibility: visible;
}

.wechat-qr img {
  width: 120px;
  height: 120px;
  display: block;
}

/* Footer Bottom */
.footer-bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-6) 0;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.footer-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.footer-info p {
  margin: 0;
}

.footer-info a {
  color: var(--color-text-secondary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.footer-info a:hover {
  color: var(--color-primary);
}

/* Back to Top Button */
.back-to-top {
  position: fixed;
  bottom: var(--spacing-6);
  right: var(--spacing-6);
  z-index: var(--z-fixed);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background-color: var(--color-primary);
  color: var(--color-text-inverse);
  border: none;
  border-radius: var(--radius-full);
  box-shadow: var(--shadow-lg);
  cursor: pointer;
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
  transition: all var(--transition-normal);
}

.back-to-top.visible {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.back-to-top:hover {
  background-color: var(--color-primary-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

/* Site Statistics */
.site-stats {
  background-color: var(--color-bg-tertiary);
  border-top: 1px solid var(--color-border-primary);
  padding: var(--spacing-6) 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-6);
  text-align: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--color-primary);
  margin-bottom: var(--spacing-1);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

/* Loading Indicator */
.loading-indicator {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: var(--z-modal);
  background-color: var(--color-bg-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.loading-indicator.active {
  opacity: 1;
  visibility: visible;
}

.loading-spinner {
  width: 48px;
  height: 48px;
}

.spinner-ring {
  width: 100%;
  height: 100%;
  border: 4px solid var(--color-border-primary);
  border-top: 4px solid var(--color-primary);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

/* ===== Responsive Design ===== */
@media (max-width: 1023px) {
  .content-wrapper {
    grid-template-columns: 1fr;
  }

  .sidebar {
    order: -1;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-6);
  }
}

@media (max-width: 767px) {
  /* Header */
  .header-content {
    padding: var(--spacing-3) 0;
  }

  .menu-toggle {
    display: flex;
  }

  .nav-menu {
    position: fixed;
    top: 100%;
    left: 0;
    right: 0;
    z-index: var(--z-dropdown);
    flex-direction: column;
    gap: 0;
    background-color: var(--color-bg-primary);
    border-top: 1px solid var(--color-border-primary);
    box-shadow: var(--shadow-lg);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-normal);
  }

  .nav-menu.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }

  .nav-menu li {
    border-bottom: 1px solid var(--color-border-primary);
  }

  .nav-menu li:last-child {
    border-bottom: none;
  }

  .nav-menu a {
    padding: var(--spacing-4) var(--spacing-6);
  }

  .nav-menu a::after {
    display: none;
  }

  /* Main Content */
  .main-content {
    padding: var(--spacing-6) 0;
  }

  /* Posts */
  .posts-container[data-layout="grid"] {
    grid-template-columns: 1fr;
  }

  .posts-container[data-layout="magazine"] {
    grid-template-columns: 1fr;
  }

  .post-content {
    padding: var(--spacing-4);
  }

  /* Sidebar */
  .sidebar {
    grid-template-columns: 1fr;
  }

  /* Footer */
  .footer-widgets {
    grid-template-columns: 1fr;
    gap: var(--spacing-6);
    padding: var(--spacing-8) 0;
  }

  .footer-bottom {
    flex-direction: column;
    gap: var(--spacing-4);
    text-align: center;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-4);
  }

  /* Back to Top */
  .back-to-top {
    bottom: var(--spacing-4);
    right: var(--spacing-4);
    width: 44px;
    height: 44px;
  }
}

@media (max-width: 479px) {
  .container {
    padding: 0 var(--spacing-3);
  }

  .search-container {
    width: 95%;
    padding: var(--spacing-4);
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .page-navigator {
    flex-wrap: wrap;
    justify-content: center;
  }
}

/* ===== Layout Specific Styles ===== */

/* List Layout */
.posts-container[data-layout="list"] .post-card {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-6);
}

.posts-container[data-layout="list"] .post-thumbnail {
  flex-shrink: 0;
  width: 200px;
  aspect-ratio: 4/3;
}

.posts-container[data-layout="list"] .post-content {
  flex: 1;
  padding: var(--spacing-4) 0;
}

.posts-container[data-layout="list"] .post-title {
  font-size: var(--font-size-2xl);
  margin-bottom: var(--spacing-3);
}

.posts-container[data-layout="list"] .post-excerpt {
  font-size: var(--font-size-lg);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-6);
}

/* Magazine Layout */
.posts-container[data-layout="magazine"] {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-8);
}

.posts-container[data-layout="magazine"] .post-card:first-child {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
}

.posts-container[data-layout="magazine"] .post-card:first-child .post-thumbnail {
  aspect-ratio: 21/9;
  margin-bottom: var(--spacing-6);
}

.posts-container[data-layout="magazine"] .post-card:first-child .post-title {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--spacing-4);
}

.posts-container[data-layout="magazine"] .post-card:first-child .post-excerpt {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-6);
}

.posts-container[data-layout="magazine"] .post-card:not(:first-child) {
  display: flex;
  flex-direction: column;
  height: fit-content;
}

.posts-container[data-layout="magazine"] .post-card:not(:first-child) .post-thumbnail {
  aspect-ratio: 16/10;
}

.posts-container[data-layout="magazine"] .post-card:not(:first-child) .post-title {
  font-size: var(--font-size-lg);
  line-height: var(--line-height-snug);
}

.posts-container[data-layout="magazine"] .post-card:not(:first-child) .post-excerpt {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Grid Layout (Default) */
.posts-container[data-layout="grid"] .post-card {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.posts-container[data-layout="grid"] .post-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.posts-container[data-layout="grid"] .post-footer {
  margin-top: auto;
}

/* Featured Posts Specific Styles */
.featured-posts .post-card {
  position: relative;
  background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
  color: var(--color-text-inverse);
  overflow: hidden;
}

.featured-posts .post-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(0,0,0,0.3), rgba(0,0,0,0.1));
  z-index: 1;
}

.featured-posts .post-content {
  position: relative;
  z-index: 2;
}

.featured-posts .post-meta,
.featured-posts .post-title,
.featured-posts .post-excerpt {
  color: var(--color-text-inverse);
}

.featured-posts .post-tags a {
  background-color: rgba(255, 255, 255, 0.2);
  color: var(--color-text-inverse);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.featured-posts .read-more-btn {
  background-color: rgba(255, 255, 255, 0.2);
  color: var(--color-text-inverse);
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.featured-posts .read-more-btn:hover {
  background-color: var(--color-text-inverse);
  color: var(--color-primary);
}

/* Responsive Layout Adjustments */
@media (max-width: 1023px) {
  .posts-container[data-layout="magazine"] {
    grid-template-columns: 1fr;
  }

  .posts-container[data-layout="magazine"] .post-card:first-child {
    grid-column: 1;
  }
}

@media (max-width: 767px) {
  .posts-container[data-layout="list"] .post-card {
    flex-direction: column;
  }

  .posts-container[data-layout="list"] .post-thumbnail {
    width: 100%;
    aspect-ratio: 16/9;
  }

  .posts-container[data-layout="list"] .post-content {
    padding: var(--spacing-4);
  }

  .posts-container[data-layout="magazine"] .post-card:first-child .post-title {
    font-size: var(--font-size-2xl);
  }

  .featured-grid {
    grid-template-columns: 1fr;
  }
}

/* ===== Animation Enhancements ===== */
.post-card {
  animation: fadeInUp 0.6s ease-out;
}

.post-card:nth-child(2) { animation-delay: 0.1s; }
.post-card:nth-child(3) { animation-delay: 0.2s; }
.post-card:nth-child(4) { animation-delay: 0.3s; }
.post-card:nth-child(5) { animation-delay: 0.4s; }
.post-card:nth-child(6) { animation-delay: 0.5s; }

/* Hover Effects */
.post-card .post-title a {
  position: relative;
  overflow: hidden;
}

.post-card .post-title a::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--color-primary);
  transition: width var(--transition-fast);
}

.post-card:hover .post-title a::after {
  width: 100%;
}

/* Loading States */
.post-card.loading {
  opacity: 0.7;
  pointer-events: none;
}

.post-card.loading .post-thumbnail {
  background: linear-gradient(90deg, var(--color-bg-secondary) 25%, var(--color-bg-tertiary) 50%, var(--color-bg-secondary) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* ===== Single Post Styles ===== */
.single-post-main {
  padding: var(--spacing-6) 0 var(--spacing-12);
}

.post-article {
  background-color: var(--color-bg-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--spacing-8);
}

/* Post Header */
.post-header {
  padding: var(--spacing-8) var(--spacing-8) var(--spacing-6);
  border-bottom: 1px solid var(--color-border-primary);
}

.post-header .post-title {
  font-size: var(--font-size-4xl);
  font-weight: 700;
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-6);
  color: var(--color-text-primary);
}

.post-header .post-meta {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.meta-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.post-header .post-thumbnail {
  margin-top: var(--spacing-6);
  border-radius: var(--radius-lg);
  overflow: hidden;
  aspect-ratio: 21/9;
}

.post-header .post-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Reading Progress Bar */
.reading-progress {
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  height: 4px;
  background-color: var(--color-bg-secondary);
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary), var(--color-accent));
  width: 0%;
  transition: width 0.1s ease-out;
}

/* Table of Contents */
.table-of-contents {
  position: sticky;
  top: 80px;
  float: right;
  width: 280px;
  margin: 0 0 var(--spacing-6) var(--spacing-6);
  background-color: var(--color-bg-secondary);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  z-index: var(--z-dropdown);
}

.toc-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-4) var(--spacing-5);
  border-bottom: 1px solid var(--color-border-primary);
}

.toc-header h3 {
  margin: 0;
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--color-text-primary);
}

.toc-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.toc-toggle:hover {
  background-color: var(--color-bg-tertiary);
  color: var(--color-text-primary);
}

.toc-nav {
  max-height: 400px;
  overflow-y: auto;
  padding: var(--spacing-4) 0;
  transition: max-height var(--transition-normal);
}

.toc-nav.collapsed {
  max-height: 0;
  padding: 0;
  overflow: hidden;
}

.toc-list {
  margin: 0;
  padding: 0;
  list-style: none;
}

.toc-item {
  margin: 0;
}

.toc-link {
  display: block;
  padding: var(--spacing-2) var(--spacing-5);
  color: var(--color-text-secondary);
  text-decoration: none;
  font-size: var(--font-size-sm);
  line-height: var(--line-height-snug);
  border-left: 3px solid transparent;
  transition: all var(--transition-fast);
}

.toc-link:hover,
.toc-link.active {
  color: var(--color-primary);
  background-color: var(--color-primary-light);
  border-left-color: var(--color-primary);
}

.toc-h2 .toc-link { padding-left: var(--spacing-5); }
.toc-h3 .toc-link { padding-left: var(--spacing-6); }
.toc-h4 .toc-link { padding-left: var(--spacing-8); }
.toc-h5 .toc-link { padding-left: var(--spacing-10); }
.toc-h6 .toc-link { padding-left: var(--spacing-12); }

/* Post Content */
.post-content {
  padding: var(--spacing-8);
}

.post-body {
  font-size: var(--font-size-lg);
  line-height: var(--line-height-relaxed);
  color: var(--color-text-primary);
}

.post-body h1,
.post-body h2,
.post-body h3,
.post-body h4,
.post-body h5,
.post-body h6 {
  margin: var(--spacing-8) 0 var(--spacing-4) 0;
  font-weight: 600;
  line-height: var(--line-height-tight);
  color: var(--color-text-primary);
  scroll-margin-top: 100px;
}

.post-body h1:first-child,
.post-body h2:first-child,
.post-body h3:first-child,
.post-body h4:first-child,
.post-body h5:first-child,
.post-body h6:first-child {
  margin-top: 0;
}

.post-body h1 { font-size: var(--font-size-3xl); }
.post-body h2 { font-size: var(--font-size-2xl); }
.post-body h3 { font-size: var(--font-size-xl); }
.post-body h4 { font-size: var(--font-size-lg); }
.post-body h5 { font-size: var(--font-size-base); }
.post-body h6 { font-size: var(--font-size-sm); }

.post-body p {
  margin: 0 0 var(--spacing-6) 0;
  line-height: var(--line-height-relaxed);
}

.post-body ul,
.post-body ol {
  margin: 0 0 var(--spacing-6) 0;
  padding-left: var(--spacing-6);
}

.post-body li {
  margin-bottom: var(--spacing-2);
  line-height: var(--line-height-relaxed);
}

.post-body blockquote {
  margin: var(--spacing-6) 0;
  padding: var(--spacing-4) var(--spacing-6);
  background-color: var(--color-bg-secondary);
  border-left: 4px solid var(--color-primary);
  border-radius: 0 var(--radius-md) var(--radius-md) 0;
  font-style: italic;
  color: var(--color-text-secondary);
}

.post-body blockquote p:last-child {
  margin-bottom: 0;
}

/* Code Blocks */
.post-body pre {
  margin: var(--spacing-6) 0;
  padding: var(--spacing-6);
  background-color: var(--color-bg-tertiary);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-lg);
  overflow-x: auto;
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
}

.post-body code {
  padding: var(--spacing-1) var(--spacing-2);
  background-color: var(--color-bg-secondary);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-sm);
  font-family: var(--font-family-mono);
  font-size: 0.9em;
  color: var(--color-primary);
}

.post-body pre code {
  padding: 0;
  background: none;
  border: none;
  color: inherit;
}

/* Images */
.post-body img {
  max-width: 100%;
  height: auto;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  margin: var(--spacing-6) 0;
  cursor: pointer;
  transition: transform var(--transition-normal);
}

.post-body img:hover {
  transform: scale(1.02);
}

/* Tables */
.post-body table {
  width: 100%;
  margin: var(--spacing-6) 0;
  border-collapse: collapse;
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-md);
  overflow: hidden;
}

.post-body th,
.post-body td {
  padding: var(--spacing-3) var(--spacing-4);
  text-align: left;
  border-bottom: 1px solid var(--color-border-primary);
}

.post-body th {
  background-color: var(--color-bg-secondary);
  font-weight: 600;
  color: var(--color-text-primary);
}

.post-body tr:last-child td {
  border-bottom: none;
}

.post-body tr:nth-child(even) {
  background-color: var(--color-bg-secondary);
}

/* Post Tags */
.post-article .post-tags {
  padding: 0 var(--spacing-8) var(--spacing-6);
  border-bottom: 1px solid var(--color-border-primary);
}

.post-article .post-tags .tags-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-3);
  font-weight: 500;
  color: var(--color-text-secondary);
}

.post-article .post-tags a {
  display: inline-block;
  margin: 0 var(--spacing-2) var(--spacing-2) 0;
  padding: var(--spacing-2) var(--spacing-4);
  background-color: var(--color-bg-secondary);
  color: var(--color-text-secondary);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-full);
  text-decoration: none;
  font-size: var(--font-size-sm);
  transition: all var(--transition-fast);
}

.post-article .post-tags a:hover {
  background-color: var(--color-primary);
  color: var(--color-text-inverse);
  border-color: var(--color-primary);
  transform: translateY(-1px);
}

/* Social Share */
.post-share {
  padding: var(--spacing-6) var(--spacing-8);
  border-bottom: 1px solid var(--color-border-primary);
}

.share-label {
  display: block;
  margin-bottom: var(--spacing-4);
  font-weight: 500;
  color: var(--color-text-secondary);
}

.share-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-3);
}

.share-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-4);
  background-color: var(--color-bg-secondary);
  color: var(--color-text-secondary);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-md);
  text-decoration: none;
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.share-btn:hover {
  background-color: var(--color-primary);
  color: var(--color-text-inverse);
  border-color: var(--color-primary);
  transform: translateY(-1px);
}

.share-btn.weibo-share:hover {
  background-color: #e6162d;
  border-color: #e6162d;
}

.share-btn.wechat-share:hover {
  background-color: #07c160;
  border-color: #07c160;
}

.share-btn.qq-share:hover {
  background-color: #12b7f5;
  border-color: #12b7f5;
}

/* Post Like */
.post-like {
  padding: var(--spacing-6) var(--spacing-8);
  text-align: center;
  border-bottom: 1px solid var(--color-border-primary);
}

.like-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-6);
  background-color: var(--color-bg-secondary);
  color: var(--color-text-secondary);
  border: 2px solid var(--color-border-primary);
  border-radius: var(--radius-full);
  font-size: var(--font-size-base);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.like-btn:hover {
  background-color: #ff6b6b;
  color: var(--color-text-inverse);
  border-color: #ff6b6b;
  transform: scale(1.05);
}

.like-btn.liked {
  background-color: #ff6b6b;
  color: var(--color-text-inverse);
  border-color: #ff6b6b;
}

.like-btn .heart-icon {
  width: 20px;
  height: 20px;
  transition: transform var(--transition-fast);
}

.like-btn:hover .heart-icon,
.like-btn.liked .heart-icon {
  transform: scale(1.2);
}

/* Copyright Notice */
.post-copyright {
  padding: var(--spacing-6) var(--spacing-8);
  background-color: var(--color-bg-secondary);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-md);
  margin: var(--spacing-6) var(--spacing-8) 0;
}

.post-copyright h4 {
  margin-bottom: var(--spacing-3);
  font-size: var(--font-size-base);
  color: var(--color-text-primary);
}

.post-copyright p {
  margin: 0;
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
  color: var(--color-text-secondary);
}

.post-copyright a {
  color: var(--color-primary);
  text-decoration: none;
}

.post-copyright a:hover {
  text-decoration: underline;
}

/* Post Navigation */
.post-navigation {
  margin-bottom: var(--spacing-8);
}

.nav-links {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4);
}

.nav-previous,
.nav-next {
  display: flex;
  flex-direction: column;
  padding: var(--spacing-6);
  background-color: var(--color-bg-primary);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-lg);
  text-decoration: none;
  transition: all var(--transition-fast);
}

.nav-previous:hover,
.nav-next:hover {
  background-color: var(--color-bg-secondary);
  border-color: var(--color-primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.nav-next {
  text-align: right;
}

.nav-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-tertiary);
  margin-bottom: var(--spacing-2);
}

.nav-title {
  font-size: var(--font-size-lg);
  font-weight: 500;
  color: var(--color-text-primary);
  line-height: var(--line-height-snug);
}

/* Related Posts */
.related-posts {
  margin-bottom: var(--spacing-8);
}

.related-posts .section-title {
  margin-bottom: var(--spacing-6);
  font-size: var(--font-size-2xl);
  font-weight: 600;
  color: var(--color-text-primary);
}

.related-posts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-6);
}

.related-post-item {
  background-color: var(--color-bg-primary);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  transition: all var(--transition-fast);
}

.related-post-item:hover {
  border-color: var(--color-primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.related-post-link {
  text-decoration: none;
  color: inherit;
}

.related-post-title {
  margin-bottom: var(--spacing-3);
  font-size: var(--font-size-lg);
  font-weight: 500;
  color: var(--color-text-primary);
  line-height: var(--line-height-snug);
  transition: color var(--transition-fast);
}

.related-post-item:hover .related-post-title {
  color: var(--color-primary);
}

.related-post-date {
  font-size: var(--font-size-sm);
  color: var(--color-text-tertiary);
}

/* ===== Comments Section ===== */
.comments-section {
  background-color: var(--color-bg-primary);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.comments-header {
  padding: var(--spacing-6) var(--spacing-8);
  background-color: var(--color-bg-secondary);
  border-bottom: 1px solid var(--color-border-primary);
}

.comments-title {
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--color-text-primary);
}

.comments-count {
  color: var(--color-text-secondary);
  font-weight: normal;
}

/* Comment List */
.comment-list {
  padding: var(--spacing-6) var(--spacing-8);
}

.comment-item {
  margin-bottom: var(--spacing-8);
  padding-bottom: var(--spacing-8);
  border-bottom: 1px solid var(--color-border-primary);
}

.comment-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.comment-main {
  display: flex;
  gap: var(--spacing-4);
}

.comment-avatar {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  border-radius: var(--radius-full);
  overflow: hidden;
}

.comment-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.comment-body {
  flex: 1;
  min-width: 0;
}

.comment-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-3);
}

.comment-author {
  font-weight: 500;
  color: var(--color-text-primary);
}

.comment-author.admin {
  color: var(--color-primary);
}

.comment-date {
  font-size: var(--font-size-sm);
  color: var(--color-text-tertiary);
}

.comment-content {
  margin-bottom: var(--spacing-4);
  line-height: var(--line-height-relaxed);
  color: var(--color-text-primary);
}

.comment-content p {
  margin: 0 0 var(--spacing-3) 0;
}

.comment-content p:last-child {
  margin-bottom: 0;
}

.comment-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.comment-reply-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  padding: var(--spacing-1) var(--spacing-2);
  background: none;
  border: none;
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.comment-reply-btn:hover {
  background-color: var(--color-bg-secondary);
  color: var(--color-primary);
}

/* Comment Replies */
.comment-children {
  margin-top: var(--spacing-6);
  padding-left: var(--spacing-12);
  border-left: 2px solid var(--color-border-primary);
}

.comment-children .comment-item {
  margin-bottom: var(--spacing-6);
  padding-bottom: var(--spacing-6);
}

.comment-children .comment-avatar {
  width: 40px;
  height: 40px;
}

/* Comment Form */
.comment-form-section {
  padding: var(--spacing-8);
  background-color: var(--color-bg-secondary);
  border-top: 1px solid var(--color-border-primary);
}

.comment-form-title {
  margin-bottom: var(--spacing-6);
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--color-text-primary);
}

.comment-form {
  display: grid;
  gap: var(--spacing-4);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4);
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-label {
  margin-bottom: var(--spacing-2);
  font-weight: 500;
  color: var(--color-text-primary);
}

.form-label.required::after {
  content: ' *';
  color: var(--color-error);
}

.form-input,
.form-textarea {
  padding: var(--spacing-3) var(--spacing-4);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-md);
  background-color: var(--color-bg-primary);
  color: var(--color-text-primary);
  font-size: var(--font-size-base);
  transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-textarea {
  min-height: 120px;
  resize: vertical;
  font-family: inherit;
  line-height: var(--line-height-relaxed);
}

/* Emoji Picker */
.emoji-picker {
  margin-bottom: var(--spacing-4);
  padding: var(--spacing-4);
  background-color: var(--color-bg-primary);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-md);
}

.emoji-picker-title {
  margin-bottom: var(--spacing-3);
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--color-text-secondary);
}

.emoji-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-2);
}

.emoji-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: none;
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.emoji-btn:hover {
  background-color: var(--color-bg-secondary);
  border-color: var(--color-primary);
  transform: scale(1.1);
}

.comment-submit {
  justify-self: start;
  padding: var(--spacing-3) var(--spacing-6);
  background-color: var(--color-primary);
  color: var(--color-text-inverse);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.comment-submit:hover {
  background-color: var(--color-primary-hover);
  transform: translateY(-1px);
}

.comment-submit:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* No Comments */
.no-comments {
  padding: var(--spacing-12) var(--spacing-8);
  text-align: center;
  color: var(--color-text-secondary);
}

.no-comments-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto var(--spacing-4);
  color: var(--color-text-tertiary);
}

.no-comments h3 {
  margin-bottom: var(--spacing-2);
  color: var(--color-text-primary);
}

.no-comments p {
  margin: 0;
  font-size: var(--font-size-lg);
}

/* ===== Image Lightbox ===== */
.lightbox-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: var(--z-modal);
  background-color: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.lightbox-overlay.active {
  opacity: 1;
  visibility: visible;
}

.lightbox-content {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.lightbox-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
}

.lightbox-close {
  position: absolute;
  top: -50px;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  border-radius: var(--radius-full);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.lightbox-close:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.lightbox-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  border-radius: var(--radius-full);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.lightbox-nav:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.lightbox-prev {
  left: -70px;
}

.lightbox-next {
  right: -70px;
}

/* ===== Responsive Design for Post ===== */
@media (max-width: 1023px) {
  .table-of-contents {
    position: static;
    float: none;
    width: 100%;
    margin: 0 0 var(--spacing-6) 0;
  }

  .post-content {
    padding: var(--spacing-6);
  }

  .post-header {
    padding: var(--spacing-6) var(--spacing-6) var(--spacing-4);
  }

  .post-share,
  .post-like,
  .post-copyright {
    padding: var(--spacing-4) var(--spacing-6);
  }

  .post-article .post-tags {
    padding: 0 var(--spacing-6) var(--spacing-4);
  }
}

@media (max-width: 767px) {
  .post-header .post-title {
    font-size: var(--font-size-2xl);
  }

  .post-header .post-meta {
    flex-direction: column;
    gap: var(--spacing-2);
  }

  .post-content {
    padding: var(--spacing-4);
  }

  .post-header {
    padding: var(--spacing-4) var(--spacing-4) var(--spacing-3);
  }

  .post-body {
    font-size: var(--font-size-base);
  }

  .post-body h1 { font-size: var(--font-size-2xl); }
  .post-body h2 { font-size: var(--font-size-xl); }
  .post-body h3 { font-size: var(--font-size-lg); }

  .share-buttons {
    justify-content: center;
  }

  .nav-links {
    grid-template-columns: 1fr;
  }

  .nav-next {
    text-align: left;
  }

  .related-posts-grid {
    grid-template-columns: 1fr;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .comment-children {
    padding-left: var(--spacing-6);
  }

  .comment-main {
    gap: var(--spacing-3);
  }

  .comment-avatar {
    width: 40px !important;
    height: 40px !important;
  }

  .lightbox-close {
    top: 20px;
    right: 20px;
  }

  .lightbox-prev {
    left: 20px;
  }

  .lightbox-next {
    right: 20px;
  }
}

@media (max-width: 479px) {
  .post-header .post-title {
    font-size: var(--font-size-xl);
  }

  .post-content,
  .post-header,
  .post-share,
  .post-like,
  .post-copyright,
  .post-article .post-tags {
    padding-left: var(--spacing-3);
    padding-right: var(--spacing-3);
  }

  .comments-header,
  .comment-list,
  .comment-form-section {
    padding-left: var(--spacing-4);
    padding-right: var(--spacing-4);
  }

  .share-buttons {
    flex-direction: column;
  }

  .share-btn {
    justify-content: center;
  }
}

/* ===== Archive Page Styles ===== */
.archive-main {
  margin-bottom: var(--spacing-8);
}

.archive-header {
  margin-bottom: var(--spacing-8);
  padding: var(--spacing-8) 0;
  text-align: center;
  background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
  color: var(--color-text-inverse);
  border-radius: var(--radius-lg);
  position: relative;
  overflow: hidden;
}

.archive-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(0,0,0,0.1), rgba(255,255,255,0.1));
  z-index: 1;
}

.archive-header > * {
  position: relative;
  z-index: 2;
}

.archive-title {
  margin-bottom: var(--spacing-4);
  font-size: var(--font-size-4xl);
  font-weight: 700;
  color: var(--color-text-inverse);
}

.archive-description {
  margin-bottom: var(--spacing-4);
  font-size: var(--font-size-lg);
  line-height: var(--line-height-relaxed);
  color: rgba(255, 255, 255, 0.9);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.archive-meta {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-4);
  font-size: var(--font-size-base);
  color: rgba(255, 255, 255, 0.9);
}

.posts-count {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

/* Timeline Layout */
.timeline-container {
  position: relative;
}

.timeline-year {
  margin-bottom: var(--spacing-12);
  position: relative;
}

.year-title {
  position: sticky;
  top: 80px;
  z-index: var(--z-sticky);
  display: inline-block;
  margin-bottom: var(--spacing-6);
  padding: var(--spacing-3) var(--spacing-6);
  background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
  color: var(--color-text-inverse);
  font-size: var(--font-size-2xl);
  font-weight: 700;
  border-radius: var(--radius-full);
  box-shadow: var(--shadow-lg);
}

.year-posts {
  position: relative;
  padding-left: var(--spacing-8);
}

.year-posts::before {
  content: '';
  position: absolute;
  left: var(--spacing-4);
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, var(--color-primary), var(--color-accent));
  border-radius: 1px;
}

.timeline-month {
  margin-bottom: var(--spacing-8);
  position: relative;
}

.month-title {
  position: relative;
  display: inline-block;
  margin-bottom: var(--spacing-4);
  padding: var(--spacing-2) var(--spacing-4);
  background-color: var(--color-bg-secondary);
  color: var(--color-text-primary);
  font-size: var(--font-size-lg);
  font-weight: 600;
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-md);
  margin-left: calc(-1 * var(--spacing-8));
}

.month-title::before {
  content: '';
  position: absolute;
  left: calc(-1 * var(--spacing-4) - 6px);
  top: 50%;
  transform: translateY(-50%);
  width: 12px;
  height: 12px;
  background-color: var(--color-primary);
  border: 3px solid var(--color-bg-primary);
  border-radius: var(--radius-full);
  box-shadow: var(--shadow-sm);
}

.month-posts {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.timeline-post {
  display: flex;
  gap: var(--spacing-4);
  padding: var(--spacing-4);
  background-color: var(--color-bg-primary);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  position: relative;
}

.timeline-post:hover {
  border-color: var(--color-primary);
  transform: translateX(4px);
  box-shadow: var(--shadow-md);
}

.timeline-post::before {
  content: '';
  position: absolute;
  left: calc(-1 * var(--spacing-8) - 4px);
  top: var(--spacing-6);
  width: 8px;
  height: 8px;
  background-color: var(--color-accent);
  border: 2px solid var(--color-bg-primary);
  border-radius: var(--radius-full);
  transition: all var(--transition-fast);
}

.timeline-post:hover::before {
  background-color: var(--color-primary);
  transform: scale(1.2);
}

.timeline-date {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
  color: var(--color-text-inverse);
  border-radius: var(--radius-lg);
  font-weight: 700;
  font-size: var(--font-size-lg);
}

.timeline-content {
  flex: 1;
  min-width: 0;
}

.timeline-title {
  margin-bottom: var(--spacing-2);
  font-size: var(--font-size-lg);
  font-weight: 600;
  line-height: var(--line-height-snug);
}

.timeline-title a {
  color: var(--color-text-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.timeline-title a:hover {
  color: var(--color-primary);
}

.timeline-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-3);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.timeline-excerpt {
  color: var(--color-text-secondary);
  line-height: var(--line-height-relaxed);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Responsive Timeline */
@media (max-width: 767px) {
  .archive-header {
    padding: var(--spacing-6) var(--spacing-4);
  }

  .archive-title {
    font-size: var(--font-size-2xl);
  }

  .year-posts {
    padding-left: var(--spacing-6);
  }

  .year-posts::before {
    left: var(--spacing-3);
  }

  .month-title {
    margin-left: calc(-1 * var(--spacing-6));
  }

  .month-title::before {
    left: calc(-1 * var(--spacing-3) - 6px);
  }

  .timeline-post {
    flex-direction: column;
    gap: var(--spacing-3);
  }

  .timeline-post::before {
    left: calc(-1 * var(--spacing-6) - 4px);
    top: var(--spacing-4);
  }

  .timeline-date {
    align-self: flex-start;
    width: 40px;
    height: 40px;
    font-size: var(--font-size-base);
  }
}

/* ===== 404 Error Page Styles ===== */
.error-page {
  min-height: calc(100vh - 200px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-8) 0;
}

.error-content {
  max-width: 800px;
  text-align: center;
  animation: fadeInUp 0.8s ease-out;
}

.error-illustration {
  margin-bottom: var(--spacing-8);
  position: relative;
}

.error-number {
  display: flex;
  justify-content: center;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.digit {
  font-size: 8rem;
  font-weight: 900;
  background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  animation: bounce 2s infinite;
}

.digit:nth-child(2) {
  animation-delay: 0.2s;
}

.digit:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.error-icon {
  width: 200px;
  height: 200px;
  margin: 0 auto;
  color: var(--color-text-tertiary);
  opacity: 0.6;
}

.error-message {
  margin-bottom: var(--spacing-8);
}

.error-title {
  margin-bottom: var(--spacing-4);
  font-size: var(--font-size-4xl);
  font-weight: 700;
  color: var(--color-text-primary);
}

.error-description {
  font-size: var(--font-size-lg);
  line-height: var(--line-height-relaxed);
  color: var(--color-text-secondary);
  margin: 0;
}

.error-search {
  margin-bottom: var(--spacing-12);
  padding: var(--spacing-8);
  background-color: var(--color-bg-secondary);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-lg);
}

.search-title {
  margin-bottom: var(--spacing-4);
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--color-text-primary);
}

.error-search .search-form {
  max-width: 400px;
  margin: 0 auto;
}

.error-search .search-input-group {
  position: relative;
  display: flex;
}

.error-search .search-input {
  flex: 1;
  padding: var(--spacing-4) var(--spacing-6);
  padding-right: 60px;
  font-size: var(--font-size-lg);
  border: 2px solid var(--color-border-primary);
  border-radius: var(--radius-lg);
  background-color: var(--color-bg-primary);
  transition: border-color var(--transition-fast);
}

.error-search .search-input:focus {
  outline: none;
  border-color: var(--color-primary);
}

.error-search .search-submit {
  position: absolute;
  right: var(--spacing-2);
  top: 50%;
  transform: translateY(-50%);
  width: 48px;
  height: 48px;
  background-color: var(--color-primary);
  color: var(--color-text-inverse);
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.error-search .search-submit:hover {
  background-color: var(--color-primary-hover);
  transform: translateY(-50%) scale(1.05);
}

.error-links {
  margin-bottom: var(--spacing-12);
}

.links-title {
  margin-bottom: var(--spacing-6);
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--color-text-primary);
}

.links-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-8);
}

.link-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-6);
  background-color: var(--color-bg-primary);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-lg);
  text-decoration: none;
  transition: all var(--transition-normal);
  text-align: center;
}

.link-card:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-lg);
  transform: translateY(-4px);
}

.link-icon {
  width: 48px;
  height: 48px;
  margin-bottom: var(--spacing-3);
  color: var(--color-primary);
}

.link-content {
  flex: 1;
}

.link-title {
  margin-bottom: var(--spacing-2);
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--color-text-primary);
}

.link-desc {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  line-height: var(--line-height-relaxed);
}

.error-recent {
  margin-bottom: var(--spacing-8);
}

.recent-title {
  margin-bottom: var(--spacing-6);
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--color-text-primary);
}

.error-recent .recent-posts-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-4);
  margin: 0;
  padding: 0;
  list-style: none;
}

.error-recent .recent-post-item {
  display: flex;
  gap: var(--spacing-3);
  padding: var(--spacing-4);
  background-color: var(--color-bg-primary);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
}

.error-recent .recent-post-item:hover {
  border-color: var(--color-primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.error-recent .recent-post-thumbnail {
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  border-radius: var(--radius-md);
  overflow: hidden;
}

.error-recent .recent-post-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.error-recent .recent-post-content {
  flex: 1;
  min-width: 0;
}

.error-recent .recent-post-title {
  margin-bottom: var(--spacing-1);
  font-size: var(--font-size-base);
  font-weight: 500;
  line-height: var(--line-height-snug);
}

.error-recent .recent-post-title a {
  color: var(--color-text-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.error-recent .recent-post-title a:hover {
  color: var(--color-primary);
}

.error-recent .recent-post-meta {
  font-size: var(--font-size-xs);
  color: var(--color-text-tertiary);
}

.error-actions {
  display: flex;
  justify-content: center;
  gap: var(--spacing-4);
  flex-wrap: wrap;
}

/* Responsive 404 Page */
@media (max-width: 767px) {
  .digit {
    font-size: 4rem;
  }

  .error-title {
    font-size: var(--font-size-2xl);
  }

  .error-description {
    font-size: var(--font-size-base);
  }

  .error-icon {
    width: 150px;
    height: 150px;
  }

  .links-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-3);
  }

  .link-card {
    padding: var(--spacing-4);
  }

  .error-recent .recent-posts-list {
    grid-template-columns: 1fr;
  }

  .error-actions {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 479px) {
  .digit {
    font-size: 3rem;
  }

  .error-number {
    gap: var(--spacing-2);
  }

  .error-search,
  .error-links,
  .error-recent {
    padding: var(--spacing-4);
  }

  .links-grid {
    grid-template-columns: 1fr;
  }
}

/* ===== Page Styles ===== */
.page-main {
  margin-bottom: var(--spacing-8);
}

.page-header {
  margin-bottom: var(--spacing-8);
  padding: var(--spacing-8) 0;
  text-align: center;
  background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
  color: var(--color-text-inverse);
  border-radius: var(--radius-lg);
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(0,0,0,0.1), rgba(255,255,255,0.1));
  z-index: 1;
}

.page-header > * {
  position: relative;
  z-index: 2;
}

.page-title {
  margin-bottom: var(--spacing-4);
  font-size: var(--font-size-4xl);
  font-weight: 700;
  color: var(--color-text-inverse);
}

.page-subtitle {
  margin-bottom: var(--spacing-4);
  font-size: var(--font-size-lg);
  line-height: var(--line-height-relaxed);
  color: rgba(255, 255, 255, 0.9);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.page-meta {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-4);
  font-size: var(--font-size-base);
  color: rgba(255, 255, 255, 0.9);
}

.page-date {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.page-content {
  background-color: var(--color-bg-primary);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.page-body {
  padding: var(--spacing-8);
  border-bottom: 1px solid var(--color-border-primary);
}

.page-body h1,
.page-body h2,
.page-body h3,
.page-body h4,
.page-body h5,
.page-body h6 {
  margin: var(--spacing-6) 0 var(--spacing-4) 0;
  font-weight: 600;
  line-height: var(--line-height-tight);
  color: var(--color-text-primary);
}

.page-body h1:first-child,
.page-body h2:first-child,
.page-body h3:first-child,
.page-body h4:first-child,
.page-body h5:first-child,
.page-body h6:first-child {
  margin-top: 0;
}

.page-body p {
  margin: 0 0 var(--spacing-4) 0;
  line-height: var(--line-height-relaxed);
  color: var(--color-text-primary);
}

/* ===== Friends Page Styles ===== */
.friends-section {
  padding: var(--spacing-8);
  border-bottom: 1px solid var(--color-border-primary);
}

.friends-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-6);
  font-size: var(--font-size-2xl);
  font-weight: 600;
  color: var(--color-text-primary);
}

.friends-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--spacing-6);
}

.friend-card {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-4);
  padding: var(--spacing-6);
  background-color: var(--color-bg-secondary);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
  position: relative;
}

.friend-card:hover {
  border-color: var(--color-primary);
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.friend-card.inactive {
  opacity: 0.6;
  filter: grayscale(50%);
}

.friend-avatar {
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  border-radius: var(--radius-lg);
  overflow: hidden;
  border: 2px solid var(--color-border-primary);
}

.friend-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-normal);
}

.friend-card:hover .friend-avatar img {
  transform: scale(1.1);
}

.friend-info {
  flex: 1;
  min-width: 0;
}

.friend-name {
  margin-bottom: var(--spacing-2);
  font-size: var(--font-size-lg);
  font-weight: 600;
  line-height: var(--line-height-snug);
}

.friend-name a {
  color: var(--color-text-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.friend-name a:hover {
  color: var(--color-primary);
}

.friend-description {
  margin-bottom: var(--spacing-3);
  color: var(--color-text-secondary);
  line-height: var(--line-height-relaxed);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.friend-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  font-size: var(--font-size-sm);
}

.friend-url {
  color: var(--color-text-tertiary);
  font-family: var(--font-family-mono);
}

.friend-status {
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 500;
}

.friend-status.active {
  background-color: var(--color-success);
  color: var(--color-text-inverse);
}

.friend-status.inactive {
  background-color: var(--color-error);
  color: var(--color-text-inverse);
}

.friend-actions {
  flex-shrink: 0;
}

.visit-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: var(--color-primary);
  color: var(--color-text-inverse);
  border-radius: var(--radius-md);
  text-decoration: none;
  transition: all var(--transition-fast);
}

.visit-btn:hover {
  background-color: var(--color-primary-hover);
  transform: scale(1.1);
}

/* Apply Section */
.apply-section {
  padding: var(--spacing-8);
}

.apply-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-6);
  font-size: var(--font-size-2xl);
  font-weight: 600;
  color: var(--color-text-primary);
}

.apply-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-8);
}

.apply-info h3 {
  margin-bottom: var(--spacing-4);
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--color-text-primary);
}

.requirements-list {
  margin: 0 0 var(--spacing-6) 0;
  padding-left: var(--spacing-6);
  color: var(--color-text-secondary);
  line-height: var(--line-height-relaxed);
}

.requirements-list li {
  margin-bottom: var(--spacing-2);
}

.site-info {
  background-color: var(--color-bg-secondary);
  padding: var(--spacing-4);
  border-radius: var(--radius-md);
  border: 1px solid var(--color-border-primary);
}

.info-item {
  margin-bottom: var(--spacing-2);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
  color: var(--color-text-secondary);
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item strong {
  color: var(--color-text-primary);
}

.apply-form-container h3 {
  margin-bottom: var(--spacing-4);
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--color-text-primary);
}

.apply-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

/* Responsive Friends Page */
@media (max-width: 1023px) {
  .apply-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-6);
  }

  .friends-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
}

@media (max-width: 767px) {
  .page-header {
    padding: var(--spacing-6) var(--spacing-4);
  }

  .page-title {
    font-size: var(--font-size-2xl);
  }

  .page-body,
  .friends-section,
  .apply-section {
    padding: var(--spacing-6);
  }

  .friends-grid {
    grid-template-columns: 1fr;
  }

  .friend-card {
    flex-direction: column;
    text-align: center;
  }

  .friend-meta {
    justify-content: center;
  }
}

@media (max-width: 479px) {
  .page-body,
  .friends-section,
  .apply-section {
    padding: var(--spacing-4);
  }

  .friend-card {
    padding: var(--spacing-4);
  }
}

/* ===== Accessibility Styles ===== */
.screen-reader-text {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  z-index: var(--z-modal);
  padding: var(--spacing-2) var(--spacing-4);
  background-color: var(--color-primary);
  color: var(--color-text-inverse);
  text-decoration: none;
  border-radius: var(--radius-md);
  font-weight: 500;
  transition: top var(--transition-fast);
}

.skip-link:focus {
  top: 6px;
}

/* Focus indicators for keyboard navigation */
.keyboard-navigation *:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

.keyboard-navigation button:focus,
.keyboard-navigation a:focus,
.keyboard-navigation input:focus,
.keyboard-navigation textarea:focus,
.keyboard-navigation select:focus {
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.2);
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --color-text-primary: #000000;
    --color-text-secondary: #333333;
    --color-bg-primary: #ffffff;
    --color-bg-secondary: #f5f5f5;
    --color-border-primary: #000000;
    --color-primary: #0000ff;
  }
}

/* Reduced motion support */
.reduced-motion *,
.reduced-motion *::before,
.reduced-motion *::after {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
  scroll-behavior: auto !important;
}

/* Print styles */
@media print {
  .header,
  .footer,
  .sidebar,
  .back-to-top,
  .search-overlay,
  .nav-menu,
  .social-share,
  .comments-section {
    display: none !important;
  }

  .main-content {
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .post-content,
  .page-content {
    font-size: 12pt !important;
    line-height: 1.5 !important;
    color: #000 !important;
  }

  .post-title,
  .page-title {
    font-size: 18pt !important;
    margin-bottom: 12pt !important;
  }

  a {
    color: #000 !important;
    text-decoration: underline !important;
  }

  a[href^="http"]:after {
    content: " (" attr(href) ")";
    font-size: 10pt;
    color: #666;
  }
}

/* ===== Performance Optimization Styles ===== */
/* Critical CSS loading indicator */
.css-loading {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.css-loaded .css-loading {
  opacity: 1;
}

/* Image optimization */
img {
  max-width: 100%;
  height: auto;
}

img[loading="lazy"] {
  opacity: 0;
  transition: opacity 0.3s ease;
}

img[loading="lazy"].loaded {
  opacity: 1;
}

/* Placeholder for lazy loaded images */
.image-placeholder {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Content loading states */
.content-loading {
  position: relative;
  overflow: hidden;
}

.content-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Update notification */
.update-notification {
  position: fixed;
  bottom: var(--spacing-4);
  right: var(--spacing-4);
  z-index: var(--z-modal);
  background-color: var(--color-bg-primary);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  padding: var(--spacing-4);
  max-width: 320px;
  animation: slideInUp 0.3s ease;
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.notification-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.notification-content p {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--color-text-primary);
}

.notification-content .btn {
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-sm);
}

/* ===== Dark Mode Enhancements ===== */
@media (prefers-color-scheme: dark) {
  :root {
    --color-bg-primary: #0f172a;
    --color-bg-secondary: #1e293b;
    --color-bg-tertiary: #334155;
    --color-text-primary: #f1f5f9;
    --color-text-secondary: #cbd5e1;
    --color-text-tertiary: #94a3b8;
    --color-text-inverse: #0f172a;
    --color-border-primary: #334155;
    --color-border-secondary: #475569;
    --color-primary: #3b82f6;
    --color-primary-hover: #2563eb;
    --color-accent: #8b5cf6;
    --color-success: #10b981;
    --color-warning: #f59e0b;
    --color-error: #ef4444;
    --color-info: #06b6d4;
  }

  .image-placeholder {
    background: linear-gradient(90deg, #334155 25%, #475569 50%, #334155 75%);
  }

  .content-loading::after {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
  }
}

/* Dark mode toggle styles */
[data-theme="dark"] {
  --color-bg-primary: #0f172a;
  --color-bg-secondary: #1e293b;
  --color-bg-tertiary: #334155;
  --color-text-primary: #f1f5f9;
  --color-text-secondary: #cbd5e1;
  --color-text-tertiary: #94a3b8;
  --color-text-inverse: #0f172a;
  --color-border-primary: #334155;
  --color-border-secondary: #475569;
}

/* ===== Responsive Utilities ===== */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

@media (min-width: 640px) {
  .container {
    padding: 0 var(--spacing-6);
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 var(--spacing-8);
  }
}

/* Utility classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.hidden { display: none !important; }
.visible { display: block !important; }

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Animation utilities */
.fade-in {
  animation: fadeIn 0.5s ease;
}

.slide-up {
  animation: slideUp 0.5s ease;
}

.scale-in {
  animation: scaleIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}
