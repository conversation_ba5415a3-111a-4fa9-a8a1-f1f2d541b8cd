<?php if (!defined('__TYPECHO_ROOT_DIR__')) exit; ?>
<?php $this->need('header.php'); ?>

<main class="main-content single-post-main" role="main">
    <div class="container">
        <!-- 面包屑导航 -->
        <nav class="breadcrumb" aria-label="<?php _e('面包屑导航'); ?>">
            <ol class="breadcrumb-list" itemscope itemtype="http://schema.org/BreadcrumbList">
                <?php $breadcrumb = getBreadcrumb($this); ?>
                <?php foreach ($breadcrumb as $index => $item): ?>
                    <li class="breadcrumb-item" itemprop="itemListElement" itemscope itemtype="http://schema.org/ListItem">
                        <?php if ($index < count($breadcrumb) - 1): ?>
                            <a href="<?php echo $item['url']; ?>" itemprop="item">
                                <span itemprop="name"><?php echo $item['title']; ?></span>
                            </a>
                        <?php else: ?>
                            <span itemprop="name"><?php echo $item['title']; ?></span>
                        <?php endif; ?>
                        <meta itemprop="position" content="<?php echo $index + 1; ?>">
                    </li>
                <?php endforeach; ?>
            </ol>
        </nav>
        
        <div class="content-wrapper">
            <!-- 文章内容 -->
            <article class="post-article" itemscope itemtype="http://schema.org/BlogPosting">
                <!-- 文章头部 -->
                <header class="post-header">
                    <!-- 文章标题 -->
                    <h1 class="post-title" itemprop="headline"><?php $this->title() ?></h1>
                    
                    <!-- 文章元信息 -->
                    <div class="post-meta">
                        <div class="meta-item">
                            <svg class="icon" viewBox="0 0 24 24">
                                <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z" />
                            </svg>
                            <time class="post-date" datetime="<?php $this->date('c'); ?>" itemprop="datePublished">
                                <?php $this->date('Y年m月d日'); ?>
                            </time>
                        </div>
                        
                        <div class="meta-item">
                            <svg class="icon" viewBox="0 0 24 24">
                                <path d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z" />
                            </svg>
                            <span class="post-author" itemprop="author" itemscope itemtype="http://schema.org/Person">
                                <span itemprop="name"><?php $this->author(); ?></span>
                            </span>
                        </div>
                        
                        <?php if ($this->category): ?>
                            <div class="meta-item">
                                <svg class="icon" viewBox="0 0 24 24">
                                    <path d="M5.5,7A1.5,1.5 0 0,1 4,5.5A1.5,1.5 0 0,1 5.5,4A1.5,1.5 0 0,1 7,5.5A1.5,1.5 0 0,1 5.5,7M21.41,11.58L12.41,2.58C12.05,2.22 11.55,2 11,2H4C2.89,2 2,2.89 2,4V11C2,11.55 2.22,12.05 2.59,12.41L11.58,21.41C11.95,21.77 12.45,21.99 13,21.99C13.55,21.99 14.05,21.77 14.41,21.41L21.41,14.41C21.77,14.05 21.99,13.55 21.99,13C21.99,12.45 21.77,11.95 21.41,11.58Z" />
                                </svg>
                                <span class="post-category">
                                    <?php $this->category(','); ?>
                                </span>
                            </div>
                        <?php endif; ?>
                        
                        <div class="meta-item">
                            <svg class="icon" viewBox="0 0 24 24">
                                <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z" />
                            </svg>
                            <span class="reading-time">
                                <?php echo getReadingTime($this->content); ?> <?php _e('分钟阅读'); ?>
                            </span>
                        </div>
                        
                        <?php if ($this->commentsNum > 0): ?>
                            <div class="meta-item">
                                <svg class="icon" viewBox="0 0 24 24">
                                    <path d="M9,22A1,1 0 0,1 8,21V18H4A2,2 0 0,1 2,16V4C2,2.89 2.9,2 4,2H20A2,2 0 0,1 22,4V16A2,2 0 0,1 20,18H13.9L10.2,21.71C10,21.9 9.75,22 9.5,22V22H9Z" />
                                </svg>
                                <a href="#comments" class="comments-link">
                                    <?php $this->commentsNum('%d 条评论', '暂无评论', '%d 条评论'); ?>
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- 文章缩略图 -->
                    <?php if ($this->fields->thumbnail): ?>
                        <div class="post-thumbnail">
                            <img src="<?php echo $this->fields->thumbnail; ?>" alt="<?php $this->title(); ?>" itemprop="image">
                        </div>
                    <?php endif; ?>
                </header>
                
                <!-- 阅读进度条 -->
                <div class="reading-progress">
                    <div class="progress-bar" id="reading-progress-bar"></div>
                </div>
                
                <!-- 文章内容 -->
                <div class="post-content" itemprop="articleBody">
                    <!-- 文章目录 -->
                    <div class="table-of-contents" id="table-of-contents">
                        <div class="toc-header">
                            <h3><?php _e('文章目录'); ?></h3>
                            <button class="toc-toggle" aria-label="<?php _e('切换目录显示'); ?>">
                                <svg class="icon" viewBox="0 0 24 24">
                                    <path d="M7.41,8.58L12,13.17L16.59,8.58L18,10L12,16L6,10L7.41,8.58Z" />
                                </svg>
                            </button>
                        </div>
                        <nav class="toc-nav" id="toc-nav">
                            <!-- 目录将通过JavaScript生成 -->
                        </nav>
                    </div>
                    
                    <!-- 文章正文 -->
                    <div class="post-body">
                        <?php echo $this->content(); ?>
                    </div>
                </div>
                
                <!-- 文章标签 -->
                <?php if ($this->tags): ?>
                    <div class="post-tags">
                        <span class="tags-label">
                            <svg class="icon" viewBox="0 0 24 24">
                                <path d="M5.5,7A1.5,1.5 0 0,1 4,5.5A1.5,1.5 0 0,1 5.5,4A1.5,1.5 0 0,1 7,5.5A1.5,1.5 0 0,1 5.5,7M21.41,11.58L12.41,2.58C12.05,2.22 11.55,2 11,2H4C2.89,2 2,2.89 2,4V11C2,11.55 2.22,12.05 2.59,12.41L11.58,21.41C11.95,21.77 12.45,21.99 13,21.99C13.55,21.99 14.05,21.77 14.41,21.41L21.41,14.41C21.77,14.05 21.99,13.55 21.99,13C21.99,12.45 21.77,11.95 21.41,11.58Z" />
                            </svg>
                            <?php _e('标签'); ?>:
                        </span>
                        <?php $this->tags(' ', true, 'none'); ?>
                    </div>
                <?php endif; ?>
                
                <!-- 社交分享 -->
                <div class="post-share">
                    <span class="share-label"><?php _e('分享到'); ?>:</span>
                    <div class="share-buttons">
                        <button class="share-btn weibo-share" data-url="<?php $this->permalink(); ?>" data-title="<?php $this->title(); ?>" aria-label="<?php _e('分享到微博'); ?>">
                            <svg class="icon" viewBox="0 0 24 24">
                                <path d="M8.5,8.5A1.5,1.5 0 0,0 7,10A1.5,1.5 0 0,0 8.5,11.5A1.5,1.5 0 0,0 10,10A1.5,1.5 0 0,0 8.5,8.5M10,2A8,8 0 0,1 18,10A8,8 0 0,1 10,18A8,8 0 0,1 2,10A8,8 0 0,1 10,2M10,4A6,6 0 0,0 4,10A6,6 0 0,0 10,16A6,6 0 0,0 16,10A6,6 0 0,0 10,4Z" />
                            </svg>
                            <?php _e('微博'); ?>
                        </button>
                        
                        <button class="share-btn wechat-share" data-url="<?php $this->permalink(); ?>" data-title="<?php $this->title(); ?>" aria-label="<?php _e('分享到微信'); ?>">
                            <svg class="icon" viewBox="0 0 24 24">
                                <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4Z" />
                            </svg>
                            <?php _e('微信'); ?>
                        </button>
                        
                        <button class="share-btn qq-share" data-url="<?php $this->permalink(); ?>" data-title="<?php $this->title(); ?>" aria-label="<?php _e('分享到QQ'); ?>">
                            <svg class="icon" viewBox="0 0 24 24">
                                <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4Z" />
                            </svg>
                            QQ
                        </button>
                        
                        <button class="share-btn copy-link" data-url="<?php $this->permalink(); ?>" aria-label="<?php _e('复制链接'); ?>">
                            <svg class="icon" viewBox="0 0 24 24">
                                <path d="M3,3H21V21H3V3M7.73,9.71L6.32,11.12C5.78,11.66 5.78,12.53 6.32,13.07L10.93,17.68C11.47,18.22 12.34,18.22 12.88,17.68L14.29,16.27L12.88,14.86L11.47,16.27L6.86,11.66L8.27,10.25L7.73,9.71M16.27,6.32L11.66,10.93L13.07,12.34L17.68,7.73C18.22,7.19 18.22,6.32 17.68,5.78L13.07,1.17C12.53,0.63 11.66,0.63 11.12,1.17L9.71,2.58L11.12,4L12.53,2.58L17.14,7.19L15.73,8.6L17.14,10L16.27,6.32Z" />
                            </svg>
                            <?php _e('复制链接'); ?>
                        </button>
                    </div>
                </div>
                
                <!-- 点赞按钮 -->
                <div class="post-like">
                    <button class="like-btn" data-cid="<?php echo $this->cid; ?>" aria-label="<?php _e('点赞'); ?>">
                        <svg class="icon heart-icon" viewBox="0 0 24 24">
                            <path d="M12,21.35L10.55,20.03C5.4,15.36 2,12.27 2,8.5C2,5.41 4.42,3 7.5,3C9.24,3 10.91,3.81 12,5.08C13.09,3.81 14.76,3 16.5,3C19.58,3 22,5.41 22,8.5C22,12.27 18.6,15.36 13.45,20.03L12,21.35Z" />
                        </svg>
                        <span class="like-count" id="like-count-<?php echo $this->cid; ?>">0</span>
                    </button>
                </div>
                
                <!-- 版权声明 -->
                <?php if ($this->options->showCopyright): ?>
                    <div class="post-copyright">
                        <h4><?php _e('版权声明'); ?></h4>
                        <p>
                            <?php _e('本文作者'); ?>: <strong><?php $this->author(); ?></strong><br>
                            <?php _e('本文链接'); ?>: <a href="<?php $this->permalink(); ?>"><?php $this->permalink(); ?></a><br>
                            <?php _e('版权声明'); ?>: <?php echo $this->options->copyrightText ? $this->options->copyrightText : '本博客所有文章除特别声明外，均采用 CC BY-NC-SA 4.0 许可协议。转载请注明出处！'; ?>
                        </p>
                    </div>
                <?php endif; ?>
            </article>
            
            <!-- 文章导航 -->
            <nav class="post-navigation" role="navigation" aria-label="<?php _e('文章导航'); ?>">
                <div class="nav-links">
                    <?php $this->thePrev('%s', '<div class="nav-previous">
                        <span class="nav-label">' . __('上一篇') . '</span>
                        <span class="nav-title">%s</span>
                    </div>'); ?>
                    
                    <?php $this->theNext('%s', '<div class="nav-next">
                        <span class="nav-label">' . __('下一篇') . '</span>
                        <span class="nav-title">%s</span>
                    </div>'); ?>
                </div>
            </nav>
            
            <!-- 相关文章 -->
            <?php $relatedPosts = getRelatedPosts($this, 6); ?>
            <?php if (!empty($relatedPosts)): ?>
                <section class="related-posts">
                    <h3 class="section-title"><?php _e('相关文章'); ?></h3>
                    <div class="related-posts-grid">
                        <?php foreach ($relatedPosts as $post): ?>
                            <article class="related-post-item">
                                <a href="<?php echo Typecho_Router::url('post', array('slug' => $post['slug']), Helper::options()->index); ?>" class="related-post-link">
                                    <h4 class="related-post-title"><?php echo $post['title']; ?></h4>
                                    <time class="related-post-date" datetime="<?php echo date('c', $post['created']); ?>">
                                        <?php echo date('Y-m-d', $post['created']); ?>
                                    </time>
                                </a>
                            </article>
                        <?php endforeach; ?>
                    </div>
                </section>
            <?php endif; ?>
            
            <!-- 评论区域 -->
            <?php $this->need('comments.php'); ?>
        </div>
    </div>
</main>

<!-- 结构化数据 -->
<?php outputStructuredData($this); ?>

<?php $this->need('footer.php'); ?>
