    </div><!-- .site-main -->
    
    <!-- Footer -->
    <footer class="site-footer" role="contentinfo" itemscope itemtype="http://schema.org/WPFooter">
        <div class="container">
            <!-- Footer Widgets -->
            <?php if ($this->options->footerWidgets): ?>
                <div class="footer-widgets">
                    <div class="footer-widget-area">
                        <h3 class="widget-title"><?php _e('关于本站'); ?></h3>
                        <div class="widget-content">
                            <?php echo $this->options->footerAbout ? $this->options->footerAbout : $this->options->description(); ?>
                        </div>
                    </div>
                    
                    <div class="footer-widget-area">
                        <h3 class="widget-title"><?php _e('快速链接'); ?></h3>
                        <ul class="footer-links">
                            <li><a href="<?php $this->options->siteUrl(); ?>"><?php _e('首页'); ?></a></li>
                            <?php $this->widget('Widget_Contents_Page_List')->to($pages); ?>
                            <?php while($pages->next()): ?>
                                <li><a href="<?php $pages->permalink(); ?>"><?php $pages->title(); ?></a></li>
                            <?php endwhile; ?>
                            <li><a href="<?php $this->options->feedUrl(); ?>"><?php _e('RSS订阅'); ?></a></li>
                        </ul>
                    </div>
                    
                    <div class="footer-widget-area">
                        <h3 class="widget-title"><?php _e('联系方式'); ?></h3>
                        <div class="social-links">
                            <?php if ($this->options->socialWeibo): ?>
                                <a href="<?php echo $this->options->socialWeibo; ?>" target="_blank" rel="noopener noreferrer" aria-label="<?php _e('微博'); ?>">
                                    <svg class="icon" viewBox="0 0 24 24">
                                        <path d="M8.5,8.5A1.5,1.5 0 0,0 7,10A1.5,1.5 0 0,0 8.5,11.5A1.5,1.5 0 0,0 10,10A1.5,1.5 0 0,0 8.5,8.5M10,2A8,8 0 0,1 18,10A8,8 0 0,1 10,18A8,8 0 0,1 2,10A8,8 0 0,1 10,2M10,4A6,6 0 0,0 4,10A6,6 0 0,0 10,16A6,6 0 0,0 16,10A6,6 0 0,0 10,4Z" />
                                    </svg>
                                </a>
                            <?php endif; ?>
                            
                            <?php if ($this->options->socialWechat): ?>
                                <a href="javascript:void(0)" class="wechat-toggle" aria-label="<?php _e('微信'); ?>">
                                    <svg class="icon" viewBox="0 0 24 24">
                                        <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4Z" />
                                    </svg>
                                    <div class="wechat-qr">
                                        <img src="<?php echo $this->options->socialWechat; ?>" alt="<?php _e('微信二维码'); ?>">
                                    </div>
                                </a>
                            <?php endif; ?>
                            
                            <?php if ($this->options->socialGithub): ?>
                                <a href="<?php echo $this->options->socialGithub; ?>" target="_blank" rel="noopener noreferrer" aria-label="GitHub">
                                    <svg class="icon" viewBox="0 0 24 24">
                                        <path d="M12,2A10,10 0 0,0 2,12C2,16.42 4.87,20.17 8.84,21.5C9.34,21.58 9.5,21.27 9.5,21C9.5,20.77 9.5,20.14 9.5,19.31C6.73,19.91 6.14,17.97 6.14,17.97C5.68,16.81 5.03,16.5 5.03,16.5C4.12,15.88 5.1,15.9 5.1,15.9C6.1,15.97 6.63,16.93 6.63,16.93C7.5,18.45 8.97,18 9.54,17.76C9.63,17.11 9.89,16.67 10.17,16.42C7.95,16.17 5.62,15.31 5.62,11.5C5.62,10.39 6,9.5 6.65,8.79C6.55,8.54 6.2,7.5 6.75,6.15C6.75,6.15 7.59,5.88 9.5,7.17C10.29,6.95 11.15,6.84 12,6.84C12.85,6.84 13.71,6.95 14.5,7.17C16.41,5.88 17.25,6.15 17.25,6.15C17.8,7.5 17.45,8.54 17.35,8.79C18,9.5 18.38,10.39 18.38,11.5C18.38,15.32 16.04,16.16 13.81,16.41C14.17,16.72 14.5,17.33 14.5,18.26C14.5,19.6 14.5,20.68 14.5,21C14.5,21.27 14.66,21.59 15.17,21.5C19.14,20.16 22,16.42 22,12A10,10 0 0,0 12,2Z" />
                                    </svg>
                                </a>
                            <?php endif; ?>
                            
                            <?php if ($this->options->socialEmail): ?>
                                <a href="mailto:<?php echo $this->options->socialEmail; ?>" aria-label="<?php _e('邮箱'); ?>">
                                    <svg class="icon" viewBox="0 0 24 24">
                                        <path d="M20,8L12,13L4,8V6L12,11L20,6M20,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6C22,4.89 21.1,4 20,4Z" />
                                    </svg>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Footer Bottom -->
            <div class="footer-bottom">
                <div class="footer-info">
                    <p class="copyright">
                        &copy; <?php echo date('Y'); ?> 
                        <a href="<?php $this->options->siteUrl(); ?>"><?php $this->options->title(); ?></a>
                        <?php _e('版权所有'); ?>
                    </p>
                    
                    <p class="powered-by">
                        <?php _e('由'); ?> <a href="http://typecho.org" target="_blank" rel="noopener">Typecho</a> <?php _e('强力驱动'); ?>
                        <?php if ($this->options->icp): ?>
                            | <a href="https://beian.miit.gov.cn" target="_blank" rel="noopener"><?php echo $this->options->icp; ?></a>
                        <?php endif; ?>
                    </p>
                </div>
                
                <!-- Back to Top Button -->
                <button class="back-to-top" aria-label="<?php _e('返回顶部'); ?>" title="<?php _e('返回顶部'); ?>">
                    <svg class="icon" viewBox="0 0 24 24">
                        <path d="M13,20H11V8L5.5,13.5L4.08,12.08L12,4.16L19.92,12.08L18.5,13.5L13,8V20Z" />
                    </svg>
                </button>
            </div>
        </div>
        
        <!-- Site Statistics -->
        <?php if ($this->options->showStats): ?>
            <div class="site-stats">
                <div class="container">
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-number"><?php Typecho_Widget::widget('Widget_Stat')->to($stat); echo $stat->publishedPostsNum; ?></span>
                            <span class="stat-label"><?php _e('文章'); ?></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number"><?php echo $stat->publishedCommentsNum; ?></span>
                            <span class="stat-label"><?php _e('评论'); ?></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number" id="visitor-count">
                                <?php echo getVisitorCount(); ?>
                            </span>
                            <span class="stat-label"><?php _e('访客'); ?></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number"><?php echo date('Y'); ?></span>
                            <span class="stat-label"><?php _e('建站'); ?></span>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </footer>
    
    <!-- Loading Indicator -->
    <div class="loading-indicator" aria-hidden="true">
        <div class="loading-spinner">
            <div class="spinner-ring"></div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="<?php $this->options->themeUrl('assets/js/main.js'); ?>?v=<?php echo filemtime(dirname(__FILE__) . '/assets/js/main.js'); ?>" defer></script>
    
    <!-- Lazy Loading Polyfill -->
    <script>
        if ('loading' in HTMLImageElement.prototype === false) {
            var script = document.createElement('script');
            script.src = '<?php $this->options->themeUrl('assets/js/lazysizes.min.js'); ?>';
            document.head.appendChild(script);
        }
    </script>
    
    <!-- Service Worker Registration -->
    <?php if ($this->options->enablePWA): ?>
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('<?php $this->options->themeUrl('sw.js'); ?>')
                    .then(function(registration) {
                        console.log('SW registered: ', registration);
                    })
                    .catch(function(registrationError) {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
    </script>
    <?php endif; ?>
    
    <!-- Custom Footer Code -->
    <?php if ($this->options->customFooterCode): ?>
        <?php echo $this->options->customFooterCode; ?>
    <?php endif; ?>
    
    <!-- Analytics -->
    <?php if ($this->options->googleAnalytics): ?>
        <!-- Google Analytics -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=<?php echo $this->options->googleAnalytics; ?>"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '<?php echo $this->options->googleAnalytics; ?>');
        </script>
    <?php endif; ?>
    
    <?php if ($this->options->baiduAnalytics): ?>
        <!-- Baidu Analytics -->
        <script>
            var _hmt = _hmt || [];
            (function() {
                var hm = document.createElement("script");
                hm.src = "https://hm.baidu.com/hm.js?<?php echo $this->options->baiduAnalytics; ?>";
                var s = document.getElementsByTagName("script")[0]; 
                s.parentNode.insertBefore(hm, s);
            })();
        </script>
    <?php endif; ?>
    
    <?php $this->footer(); ?>
</body>
</html>
