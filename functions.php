<?php
if (!defined('__TYPECHO_ROOT_DIR__')) exit;

/**
 * 主题配置函数
 */
function themeConfig($form) {
    // 基础设置
    $siteImage = new Typecho_Widget_Helper_Form_Element_Text('siteImage', NULL, NULL, _t('网站默认图片'), _t('用于社交媒体分享的默认图片URL'));
    $form->addInput($siteImage);
    
    $siteLogo = new Typecho_Widget_Helper_Form_Element_Text('siteLogo', NULL, NULL, _t('网站Logo'), _t('网站Logo图片URL，留空则显示文字标题'));
    $form->addInput($siteLogo);
    
    $favicon = new Typecho_Widget_Helper_Form_Element_Text('favicon', NULL, NULL, _t('网站图标'), _t('网站favicon.ico文件URL'));
    $form->addInput($favicon);
    
    $appleTouchIcon = new Typecho_Widget_Helper_Form_Element_Text('appleTouchIcon', NULL, NULL, _t('苹果触摸图标'), _t('Apple Touch Icon图片URL'));
    $form->addInput($appleTouchIcon);
    
    // 主题设置
    $themeColor = new Typecho_Widget_Helper_Form_Element_Text('themeColor', NULL, '#2563eb', _t('主题色'), _t('网站主题颜色，用于浏览器主题色显示'));
    $form->addInput($themeColor);
    
    $darkMode = new Typecho_Widget_Helper_Form_Element_Radio('darkMode', 
        array('1' => _t('启用'), '0' => _t('禁用')), 
        '1', _t('深色模式'), _t('是否启用深色/浅色模式切换功能'));
    $form->addInput($darkMode);
    
    // 首页布局
    $homeLayout = new Typecho_Widget_Helper_Form_Element_Radio('homeLayout',
        array(
            'grid' => _t('卡片网格'),
            'list' => _t('列表视图'),
            'magazine' => _t('杂志风格')
        ), 'grid', _t('首页布局'), _t('选择首页文章的显示布局'));
    $form->addInput($homeLayout);
    
    $featuredPosts = new Typecho_Widget_Helper_Form_Element_Radio('featuredPosts',
        array('1' => _t('显示'), '0' => _t('隐藏')),
        '1', _t('特色文章区域'), _t('是否在首页显示特色文章区域'));
    $form->addInput($featuredPosts);
    
    $defaultThumbnail = new Typecho_Widget_Helper_Form_Element_Text('defaultThumbnail', NULL, NULL, _t('默认缩略图'), _t('文章没有设置缩略图时使用的默认图片URL'));
    $form->addInput($defaultThumbnail);
    
    // 社交链接
    $socialWeibo = new Typecho_Widget_Helper_Form_Element_Text('socialWeibo', NULL, NULL, _t('微博链接'), _t('微博个人主页链接'));
    $form->addInput($socialWeibo);
    
    $socialWechat = new Typecho_Widget_Helper_Form_Element_Text('socialWechat', NULL, NULL, _t('微信二维码'), _t('微信二维码图片URL'));
    $form->addInput($socialWechat);
    
    $socialGithub = new Typecho_Widget_Helper_Form_Element_Text('socialGithub', NULL, NULL, _t('GitHub链接'), _t('GitHub个人主页链接'));
    $form->addInput($socialGithub);
    
    $socialEmail = new Typecho_Widget_Helper_Form_Element_Text('socialEmail', NULL, NULL, _t('邮箱地址'), _t('联系邮箱地址'));
    $form->addInput($socialEmail);
    
    // 页脚设置
    $footerWidgets = new Typecho_Widget_Helper_Form_Element_Radio('footerWidgets',
        array('1' => _t('显示'), '0' => _t('隐藏')),
        '1', _t('页脚小工具'), _t('是否显示页脚小工具区域'));
    $form->addInput($footerWidgets);
    
    $footerAbout = new Typecho_Widget_Helper_Form_Element_Textarea('footerAbout', NULL, NULL, _t('关于本站'), _t('页脚关于本站的描述文字'));
    $form->addInput($footerAbout);
    
    $icp = new Typecho_Widget_Helper_Form_Element_Text('icp', NULL, NULL, _t('ICP备案号'), _t('网站ICP备案号'));
    $form->addInput($icp);
    
    // 统计设置
    $showStats = new Typecho_Widget_Helper_Form_Element_Radio('showStats',
        array('1' => _t('显示'), '0' => _t('隐藏')),
        '1', _t('网站统计'), _t('是否显示网站统计信息'));
    $form->addInput($showStats);
    
    // 分析代码
    $googleAnalytics = new Typecho_Widget_Helper_Form_Element_Text('googleAnalytics', NULL, NULL, _t('Google Analytics'), _t('Google Analytics跟踪ID'));
    $form->addInput($googleAnalytics);
    
    $baiduAnalytics = new Typecho_Widget_Helper_Form_Element_Text('baiduAnalytics', NULL, NULL, _t('百度统计'), _t('百度统计代码ID'));
    $form->addInput($baiduAnalytics);
    
    // 高级设置
    $enablePWA = new Typecho_Widget_Helper_Form_Element_Radio('enablePWA',
        array('1' => _t('启用'), '0' => _t('禁用')),
        '0', _t('PWA支持'), _t('是否启用Progressive Web App功能'));
    $form->addInput($enablePWA);
    
    $customHeadCode = new Typecho_Widget_Helper_Form_Element_Textarea('customHeadCode', NULL, NULL, _t('自定义头部代码'), _t('在&lt;head&gt;标签内添加的自定义代码'));
    $form->addInput($customHeadCode);
    
    $customFooterCode = new Typecho_Widget_Helper_Form_Element_Textarea('customFooterCode', NULL, NULL, _t('自定义底部代码'), _t('在&lt;/body&gt;标签前添加的自定义代码'));
    $form->addInput($customFooterCode);
}

/**
 * 获取访客统计数据
 */
function getVisitorCount() {
    $db = Typecho_Db::get();
    $prefix = $db->getPrefix();
    
    // 创建访客统计表（如果不存在）
    try {
        $db->query("CREATE TABLE IF NOT EXISTS `{$prefix}visitors` (
            `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
            `ip` varchar(45) NOT NULL,
            `date` date NOT NULL,
            `count` int(10) unsigned DEFAULT 1,
            PRIMARY KEY (`id`),
            UNIQUE KEY `ip_date` (`ip`, `date`)
        ) ENGINE=MyISAM DEFAULT CHARSET=utf8");
    } catch (Exception $e) {
        return 0;
    }
    
    $ip = $_SERVER['REMOTE_ADDR'];
    $today = date('Y-m-d');
    
    // 更新今日访客记录
    try {
        $db->query($db->insert($prefix . 'visitors')
            ->rows(array('ip' => $ip, 'date' => $today))
            ->onDuplicate(array('count' => new Typecho_Db_Query_Expression('count + 1'))));
    } catch (Exception $e) {
        // 忽略错误
    }
    
    // 获取总访客数
    try {
        $result = $db->fetchRow($db->select('COUNT(DISTINCT ip) as total')
            ->from($prefix . 'visitors'));
        return $result['total'];
    } catch (Exception $e) {
        return 0;
    }
}

/**
 * 获取文章阅读时间
 */
function getReadingTime($content) {
    $wordCount = mb_strlen(strip_tags($content), 'UTF-8');
    $readingTime = ceil($wordCount / 300); // 假设每分钟阅读300字
    return max(1, $readingTime);
}

/**
 * 获取文章摘要
 */
function getExcerpt($content, $length = 150) {
    $content = strip_tags($content);
    $content = preg_replace('/\s+/', ' ', $content);
    
    if (mb_strlen($content, 'UTF-8') <= $length) {
        return $content;
    }
    
    return mb_substr($content, 0, $length, 'UTF-8') . '...';
}

/**
 * 获取文章缩略图
 */
function getThumbnail($content, $default = '') {
    // 从内容中提取第一张图片
    preg_match('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $content, $matches);
    
    if (!empty($matches[1])) {
        return $matches[1];
    }
    
    return $default;
}

/**
 * 时间格式化
 */
function timeAgo($timestamp) {
    $time = time() - $timestamp;
    
    if ($time < 60) {
        return '刚刚';
    } elseif ($time < 3600) {
        return floor($time / 60) . '分钟前';
    } elseif ($time < 86400) {
        return floor($time / 3600) . '小时前';
    } elseif ($time < 2592000) {
        return floor($time / 86400) . '天前';
    } else {
        return date('Y-m-d', $timestamp);
    }
}

/**
 * 获取相关文章
 */
function getRelatedPosts($widget, $limit = 6) {
    $db = Typecho_Db::get();
    $prefix = $db->getPrefix();
    
    // 获取当前文章的标签
    $tags = array();
    if ($widget->tags) {
        foreach ($widget->tags as $tag) {
            $tags[] = $tag['name'];
        }
    }
    
    if (empty($tags)) {
        return array();
    }
    
    // 查询相关文章
    $tagStr = "'" . implode("','", $tags) . "'";
    $sql = "SELECT DISTINCT c.cid, c.title, c.slug, c.created, c.text 
            FROM {$prefix}contents c 
            LEFT JOIN {$prefix}relationships r ON c.cid = r.cid 
            LEFT JOIN {$prefix}metas m ON r.mid = m.mid 
            WHERE m.name IN ({$tagStr}) 
            AND c.type = 'post' 
            AND c.status = 'publish' 
            AND c.cid != {$widget->cid} 
            ORDER BY c.created DESC 
            LIMIT {$limit}";
    
    try {
        return $db->fetchAll($sql);
    } catch (Exception $e) {
        return array();
    }
}

/**
 * 生成面包屑导航
 */
function getBreadcrumb($widget) {
    $breadcrumb = array();
    
    $breadcrumb[] = array(
        'title' => '首页',
        'url' => $widget->options->siteUrl
    );
    
    if ($widget->is('post')) {
        if ($widget->categories) {
            foreach ($widget->categories as $category) {
                $breadcrumb[] = array(
                    'title' => $category['name'],
                    'url' => $category['permalink']
                );
                break; // 只显示第一个分类
            }
        }
        $breadcrumb[] = array(
            'title' => $widget->title,
            'url' => $widget->permalink
        );
    } elseif ($widget->is('page')) {
        $breadcrumb[] = array(
            'title' => $widget->title,
            'url' => $widget->permalink
        );
    } elseif ($widget->is('category')) {
        $breadcrumb[] = array(
            'title' => $widget->getDescription(),
            'url' => $widget->permalink
        );
    } elseif ($widget->is('tag')) {
        $breadcrumb[] = array(
            'title' => '标签: ' . $widget->getDescription(),
            'url' => $widget->permalink
        );
    } elseif ($widget->is('author')) {
        $breadcrumb[] = array(
            'title' => '作者: ' . $widget->getDescription(),
            'url' => $widget->permalink
        );
    } elseif ($widget->is('search')) {
        $breadcrumb[] = array(
            'title' => '搜索: ' . $widget->keywords,
            'url' => $widget->permalink
        );
    }
    
    return $breadcrumb;
}

/**
 * 输出结构化数据
 */
function outputStructuredData($widget) {
    if ($widget->is('post')) {
        $structuredData = array(
            '@context' => 'https://schema.org',
            '@type' => 'BlogPosting',
            'headline' => $widget->title,
            'description' => getExcerpt($widget->content),
            'author' => array(
                '@type' => 'Person',
                'name' => $widget->author->screenName
            ),
            'publisher' => array(
                '@type' => 'Organization',
                'name' => $widget->options->title,
                'logo' => array(
                    '@type' => 'ImageObject',
                    'url' => $widget->options->siteLogo ? $widget->options->siteLogo : $widget->options->themeUrl('assets/images/logo.png')
                )
            ),
            'datePublished' => date('c', $widget->created),
            'dateModified' => date('c', $widget->modified),
            'mainEntityOfPage' => array(
                '@type' => 'WebPage',
                '@id' => $widget->permalink
            )
        );
        
        // 添加图片
        $thumbnail = getThumbnail($widget->content, $widget->options->defaultThumbnail);
        if ($thumbnail) {
            $structuredData['image'] = $thumbnail;
        }
        
        echo '<script type="application/ld+json">' . json_encode($structuredData, JSON_UNESCAPED_UNICODE) . '</script>';
    }
}

/**
 * Generate XML Sitemap
 */
function generateSitemap() {
    $db = Typecho_Db::get();
    $options = Typecho_Widget::widget('Widget_Options');

    $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

    // Homepage
    $xml .= '<url>' . "\n";
    $xml .= '<loc>' . $options->siteUrl . '</loc>' . "\n";
    $xml .= '<changefreq>daily</changefreq>' . "\n";
    $xml .= '<priority>1.0</priority>' . "\n";
    $xml .= '</url>' . "\n";

    // Posts
    $posts = $db->fetchAll($db->select()->from('table.contents')
        ->where('table.contents.status = ?', 'publish')
        ->where('table.contents.type = ?', 'post')
        ->order('table.contents.created', Typecho_Db::SORT_DESC));

    foreach ($posts as $post) {
        $xml .= '<url>' . "\n";
        $xml .= '<loc>' . Typecho_Router::url('post', $post, $options->index) . '</loc>' . "\n";
        $xml .= '<lastmod>' . date('c', $post['modified']) . '</lastmod>' . "\n";
        $xml .= '<changefreq>weekly</changefreq>' . "\n";
        $xml .= '<priority>0.8</priority>' . "\n";
        $xml .= '</url>' . "\n";
    }

    // Pages
    $pages = $db->fetchAll($db->select()->from('table.contents')
        ->where('table.contents.status = ?', 'publish')
        ->where('table.contents.type = ?', 'page'));

    foreach ($pages as $page) {
        $xml .= '<url>' . "\n";
        $xml .= '<loc>' . Typecho_Router::url('page', $page, $options->index) . '</loc>' . "\n";
        $xml .= '<lastmod>' . date('c', $page['modified']) . '</lastmod>' . "\n";
        $xml .= '<changefreq>monthly</changefreq>' . "\n";
        $xml .= '<priority>0.6</priority>' . "\n";
        $xml .= '</url>' . "\n";
    }

    // Categories
    $categories = $db->fetchAll($db->select()->from('table.metas')
        ->where('table.metas.type = ?', 'category'));

    foreach ($categories as $category) {
        $xml .= '<url>' . "\n";
        $xml .= '<loc>' . Typecho_Router::url('category', $category, $options->index) . '</loc>' . "\n";
        $xml .= '<changefreq>weekly</changefreq>' . "\n";
        $xml .= '<priority>0.5</priority>' . "\n";
        $xml .= '</url>' . "\n";
    }

    $xml .= '</urlset>';

    return $xml;
}

/**
 * Generate Robots.txt
 */
function generateRobotsTxt() {
    $options = Typecho_Widget::widget('Widget_Options');

    $robots = "User-agent: *\n";
    $robots .= "Allow: /\n";
    $robots .= "Disallow: /admin/\n";
    $robots .= "Disallow: /usr/\n";
    $robots .= "Disallow: /var/\n";
    $robots .= "Disallow: /install/\n";
    $robots .= "Disallow: /*?*\n";
    $robots .= "\n";
    $robots .= "Sitemap: " . $options->siteUrl . "sitemap.xml\n";

    return $robots;
}

/**
 * Get SEO Meta Description
 */
function getSEODescription($widget) {
    if ($widget->is('post') || $widget->is('page')) {
        // Use custom excerpt or auto-generated
        $description = $widget->fields->description ?: getExcerpt($widget, 160);
    } elseif ($widget->is('category')) {
        $description = $widget->getDescription() ?: '查看' . $widget->name . '分类下的所有文章';
    } elseif ($widget->is('tag')) {
        $description = '查看标签"' . $widget->name . '"下的所有文章';
    } elseif ($widget->is('archive')) {
        $description = '查看' . $widget->getArchiveDate() . '的文章归档';
    } else {
        $description = $widget->options->description;
    }

    return htmlspecialchars(strip_tags($description));
}

/**
 * Get SEO Keywords
 */
function getSEOKeywords($widget) {
    $keywords = array();

    if ($widget->is('post')) {
        // Get tags as keywords
        $tags = $widget->tags;
        if ($tags) {
            while ($tags->next()) {
                $keywords[] = $tags->name;
            }
        }

        // Add category
        $category = $widget->category;
        if ($category) {
            $keywords[] = $category;
        }

        // Add custom keywords
        if ($widget->fields->keywords) {
            $customKeywords = explode(',', $widget->fields->keywords);
            $keywords = array_merge($keywords, array_map('trim', $customKeywords));
        }
    } elseif ($widget->is('category')) {
        $keywords[] = $widget->name;
    } elseif ($widget->is('tag')) {
        $keywords[] = $widget->name;
    }

    // Add site keywords
    if ($widget->options->keywords) {
        $siteKeywords = explode(',', $widget->options->keywords);
        $keywords = array_merge($keywords, array_map('trim', $siteKeywords));
    }

    return implode(', ', array_unique($keywords));
}

/**
 * Generate Enhanced Structured Data
 */
function getEnhancedStructuredData($widget) {
    $options = $widget->options;
    $data = array();

    if ($widget->is('post')) {
        // Article structured data
        $data = array(
            '@context' => 'https://schema.org',
            '@type' => 'Article',
            'headline' => $widget->title,
            'description' => getSEODescription($widget),
            'image' => getThumbnail($widget->content, $options->defaultThumbnail),
            'author' => array(
                '@type' => 'Person',
                'name' => $widget->author->screenName,
                'url' => $widget->author->url ?: $options->siteUrl
            ),
            'publisher' => array(
                '@type' => 'Organization',
                'name' => $options->title,
                'logo' => array(
                    '@type' => 'ImageObject',
                    'url' => $options->siteUrl . 'assets/images/logo.png'
                )
            ),
            'datePublished' => date('c', $widget->created),
            'dateModified' => date('c', $widget->modified),
            'mainEntityOfPage' => array(
                '@type' => 'WebPage',
                '@id' => $widget->permalink
            )
        );

        // Add article section
        if ($widget->category) {
            $data['articleSection'] = $widget->category;
        }

        // Add keywords
        $keywords = getSEOKeywords($widget);
        if ($keywords) {
            $data['keywords'] = $keywords;
        }

        // Add word count
        $wordCount = mb_strlen(strip_tags($widget->content), 'UTF-8');
        $data['wordCount'] = $wordCount;

        // Add reading time
        $readingTime = getReadingTime($widget);
        $data['timeRequired'] = 'PT' . $readingTime . 'M';

    } elseif ($widget->is('index')) {
        // Website structured data
        $data = array(
            '@context' => 'https://schema.org',
            '@type' => 'WebSite',
            'name' => $options->title,
            'description' => $options->description,
            'url' => $options->siteUrl,
            'potentialAction' => array(
                '@type' => 'SearchAction',
                'target' => $options->siteUrl . '?s={search_term_string}',
                'query-input' => 'required name=search_term_string'
            ),
            'publisher' => array(
                '@type' => 'Organization',
                'name' => $options->title,
                'logo' => array(
                    '@type' => 'ImageObject',
                    'url' => $options->siteUrl . 'assets/images/logo.png'
                )
            )
        );
    }

    return json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
}

/**
 * Generate Breadcrumb Structured Data
 */
function getBreadcrumbStructuredData($widget) {
    $breadcrumbs = array(
        '@context' => 'https://schema.org',
        '@type' => 'BreadcrumbList',
        'itemListElement' => array()
    );

    $position = 1;

    // Home
    $breadcrumbs['itemListElement'][] = array(
        '@type' => 'ListItem',
        'position' => $position++,
        'name' => '首页',
        'item' => $widget->options->siteUrl
    );

    if ($widget->is('post')) {
        // Category
        if ($widget->category) {
            $breadcrumbs['itemListElement'][] = array(
                '@type' => 'ListItem',
                'position' => $position++,
                'name' => $widget->category,
                'item' => $widget->categories[0]['permalink']
            );
        }

        // Current post
        $breadcrumbs['itemListElement'][] = array(
            '@type' => 'ListItem',
            'position' => $position,
            'name' => $widget->title,
            'item' => $widget->permalink
        );
    } elseif ($widget->is('category')) {
        $breadcrumbs['itemListElement'][] = array(
            '@type' => 'ListItem',
            'position' => $position,
            'name' => $widget->name,
            'item' => $widget->permalink
        );
    } elseif ($widget->is('tag')) {
        $breadcrumbs['itemListElement'][] = array(
            '@type' => 'ListItem',
            'position' => $position,
            'name' => $widget->name,
            'item' => $widget->permalink
        );
    }

    return json_encode($breadcrumbs, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
}
?>
