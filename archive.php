<?php if (!defined('__TYPECHO_ROOT_DIR__')) exit; ?>
<?php $this->need('header.php'); ?>

<main class="main-content">
    <div class="container">
        <div class="content-wrapper">
            <div class="archive-main">
                <!-- Breadcrumb -->
                <nav class="breadcrumb" aria-label="面包屑导航">
                    <ol class="breadcrumb-list">
                        <li class="breadcrumb-item">
                            <a href="<?php $this->options->siteUrl(); ?>">首页</a>
                        </li>
                        <li class="breadcrumb-item">
                            <?php if ($this->is('category')): ?>
                                分类：<?php $this->archiveTitle(array(
                                    'category'  =>  _t('分类 %s 下的文章'),
                                    'search'    =>  _t('包含关键字 %s 的文章'),
                                    'tag'       =>  _t('标签 %s 下的文章'),
                                    'author'    =>  _t('%s 发布的文章')
                                ), '', ''); ?>
                            <?php elseif ($this->is('tag')): ?>
                                标签：<?php $this->archiveTitle(array(
                                    'category'  =>  _t('分类 %s 下的文章'),
                                    'search'    =>  _t('包含关键字 %s 的文章'),
                                    'tag'       =>  _t('标签 %s 下的文章'),
                                    'author'    =>  _t('%s 发布的文章')
                                ), '', ''); ?>
                            <?php elseif ($this->is('author')): ?>
                                作者：<?php $this->archiveTitle(array(
                                    'category'  =>  _t('分类 %s 下的文章'),
                                    'search'    =>  _t('包含关键字 %s 的文章'),
                                    'tag'       =>  _t('标签 %s 下的文章'),
                                    'author'    =>  _t('%s 发布的文章')
                                ), '', ''); ?>
                            <?php elseif ($this->is('search')): ?>
                                搜索：<?php $this->archiveTitle(array(
                                    'category'  =>  _t('分类 %s 下的文章'),
                                    'search'    =>  _t('包含关键字 %s 的文章'),
                                    'tag'       =>  _t('标签 %s 下的文章'),
                                    'author'    =>  _t('%s 发布的文章')
                                ), '', ''); ?>
                            <?php else: ?>
                                <?php $this->archiveTitle(array(
                                    'category'  =>  _t('分类 %s 下的文章'),
                                    'search'    =>  _t('包含关键字 %s 的文章'),
                                    'tag'       =>  _t('标签 %s 下的文章'),
                                    'author'    =>  _t('%s 发布的文章')
                                ), '', ''); ?>
                            <?php endif; ?>
                        </li>
                    </ol>
                </nav>

                <!-- Archive Header -->
                <header class="archive-header">
                    <h1 class="archive-title">
                        <?php $this->archiveTitle(array(
                            'category'  =>  _t('分类 %s 下的文章'),
                            'search'    =>  _t('包含关键字 %s 的文章'),
                            'tag'       =>  _t('标签 %s 下的文章'),
                            'author'    =>  _t('%s 发布的文章')
                        ), '', ''); ?>
                    </h1>
                    
                    <?php if ($this->is('category') && $this->getDescription()): ?>
                        <div class="archive-description">
                            <?php echo $this->getDescription(); ?>
                        </div>
                    <?php endif; ?>
                    
                    <div class="archive-meta">
                        <span class="posts-count">
                            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                <polyline points="14,2 14,8 20,8"></polyline>
                                <line x1="16" y1="13" x2="8" y2="13"></line>
                                <line x1="16" y1="17" x2="8" y2="17"></line>
                                <polyline points="10,9 9,9 8,9"></polyline>
                            </svg>
                            共 <?php echo $this->getTotal(); ?> 篇文章
                        </span>
                    </div>
                </header>

                <!-- Archive Content -->
                <div class="archive-content">
                    <?php if ($this->have()): ?>
                        <?php if ($this->is('search')): ?>
                            <!-- Search Results Layout -->
                            <div class="posts-container" data-layout="list">
                                <?php while($this->next()): ?>
                                    <article class="post-card">
                                        <?php if ($this->fields->thumbnail || has_post_thumbnail($this)): ?>
                                            <div class="post-thumbnail">
                                                <a href="<?php $this->permalink() ?>" title="<?php $this->title() ?>">
                                                    <img src="<?php echo get_post_thumbnail($this); ?>" 
                                                         alt="<?php $this->title() ?>" 
                                                         loading="lazy">
                                                </a>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <div class="post-content">
                                            <div class="post-meta">
                                                <time datetime="<?php $this->date('c'); ?>"><?php $this->date('Y-m-d'); ?></time>
                                                <span class="meta-separator">·</span>
                                                <span class="post-category">
                                                    <a href="<?php $this->category(',', false); ?>"><?php $this->category(',', false); ?></a>
                                                </span>
                                                <span class="meta-separator">·</span>
                                                <span class="reading-time"><?php echo getReadingTime($this->content); ?></span>
                                                <?php if ($this->commentsNum > 0): ?>
                                                    <span class="meta-separator">·</span>
                                                    <span class="post-comments">
                                                        <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                                            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                                                        </svg>
                                                        <?php $this->commentsNum('%d'); ?>
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                            
                                            <h2 class="post-title">
                                                <a href="<?php $this->permalink() ?>" title="<?php $this->title() ?>">
                                                    <?php $this->title() ?>
                                                </a>
                                            </h2>
                                            
                                            <div class="post-excerpt">
                                                <?php echo getExcerpt($this, 200); ?>
                                            </div>
                                            
                                            <?php if ($this->tags): ?>
                                                <div class="post-tags">
                                                    <?php $this->tags(', ', true, 'none'); ?>
                                                </div>
                                            <?php endif; ?>
                                            
                                            <div class="post-footer">
                                                <a href="<?php $this->permalink() ?>" class="read-more-btn">
                                                    阅读全文
                                                    <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                                        <line x1="5" y1="12" x2="19" y2="12"></line>
                                                        <polyline points="12,5 19,12 12,19"></polyline>
                                                    </svg>
                                                </a>
                                            </div>
                                        </div>
                                    </article>
                                <?php endwhile; ?>
                            </div>
                        <?php else: ?>
                            <!-- Timeline Layout for Date Archives -->
                            <div class="timeline-container">
                                <?php 
                                $currentYear = '';
                                $currentMonth = '';
                                ?>
                                <?php while($this->next()): ?>
                                    <?php 
                                    $postYear = date('Y', $this->created);
                                    $postMonth = date('n', $this->created);
                                    $postMonthName = date('m', $this->created);
                                    ?>
                                    
                                    <?php if ($postYear != $currentYear): ?>
                                        <?php if ($currentYear != ''): ?>
                                            </div> <!-- Close previous year -->
                                        <?php endif; ?>
                                        <div class="timeline-year">
                                            <h2 class="year-title"><?php echo $postYear; ?></h2>
                                            <div class="year-posts">
                                        <?php $currentYear = $postYear; ?>
                                        <?php $currentMonth = ''; ?>
                                    <?php endif; ?>
                                    
                                    <?php if ($postMonth != $currentMonth): ?>
                                        <?php if ($currentMonth != ''): ?>
                                            </div> <!-- Close previous month -->
                                        <?php endif; ?>
                                        <div class="timeline-month">
                                            <h3 class="month-title"><?php echo $postMonthName; ?>月</h3>
                                            <div class="month-posts">
                                        <?php $currentMonth = $postMonth; ?>
                                    <?php endif; ?>
                                    
                                    <article class="timeline-post">
                                        <div class="timeline-date">
                                            <span class="day"><?php $this->date('d'); ?></span>
                                        </div>
                                        <div class="timeline-content">
                                            <h4 class="timeline-title">
                                                <a href="<?php $this->permalink() ?>" title="<?php $this->title() ?>">
                                                    <?php $this->title() ?>
                                                </a>
                                            </h4>
                                            <div class="timeline-meta">
                                                <span class="post-category">
                                                    <a href="<?php $this->category(',', false); ?>"><?php $this->category(',', false); ?></a>
                                                </span>
                                                <?php if ($this->commentsNum > 0): ?>
                                                    <span class="meta-separator">·</span>
                                                    <span class="post-comments"><?php $this->commentsNum('%d 条评论'); ?></span>
                                                <?php endif; ?>
                                            </div>
                                            <div class="timeline-excerpt">
                                                <?php echo getExcerpt($this, 100); ?>
                                            </div>
                                        </div>
                                    </article>
                                <?php endwhile; ?>
                                
                                <?php if ($currentMonth != ''): ?>
                                    </div> <!-- Close last month -->
                                    </div> <!-- Close month container -->
                                <?php endif; ?>
                                
                                <?php if ($currentYear != ''): ?>
                                    </div> <!-- Close year posts -->
                                    </div> <!-- Close last year -->
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Pagination -->
                        <?php $this->pageNav('上一页', '下一页', 3, '...', array('wrapTag' => 'nav', 'wrapClass' => 'pagination', 'itemTag' => '', 'textTag' => 'span', 'currentClass' => 'current', 'prevClass' => 'prev', 'nextClass' => 'next')); ?>
                    <?php else: ?>
                        <!-- No Posts Found -->
                        <div class="no-posts">
                            <svg class="no-posts-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <circle cx="11" cy="11" r="8"></circle>
                                <path d="M21 21l-4.35-4.35"></path>
                            </svg>
                            <h2>没有找到相关文章</h2>
                            <p>
                                <?php if ($this->is('search')): ?>
                                    抱歉，没有找到包含关键字 "<?php echo $this->archiveSlug; ?>" 的文章。
                                <?php else: ?>
                                    这里还没有发布任何文章。
                                <?php endif; ?>
                            </p>
                            <a href="<?php $this->options->siteUrl(); ?>" class="btn btn-primary">返回首页</a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Sidebar -->
            <?php $this->need('sidebar.php'); ?>
        </div>
    </div>
</main>

<?php $this->need('footer.php'); ?>
