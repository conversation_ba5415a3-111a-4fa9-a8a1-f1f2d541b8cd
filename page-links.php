<?php if (!defined('__TYPECHO_ROOT_DIR__')) exit; ?>
<?php 
/**
 * 友情链接页面模板
 * 
 * @package Modern Responsive Theme
 */
?>
<?php $this->need('header.php'); ?>

<main class="main-content">
    <div class="container">
        <div class="content-wrapper">
            <div class="page-main">
                <!-- Breadcrumb -->
                <nav class="breadcrumb" aria-label="面包屑导航">
                    <ol class="breadcrumb-list">
                        <li class="breadcrumb-item">
                            <a href="<?php $this->options->siteUrl(); ?>">首页</a>
                        </li>
                        <li class="breadcrumb-item">
                            <?php $this->title() ?>
                        </li>
                    </ol>
                </nav>

                <!-- Page Header -->
                <header class="page-header">
                    <h1 class="page-title"><?php $this->title() ?></h1>
                    <?php if ($this->fields->subtitle): ?>
                        <p class="page-subtitle"><?php $this->fields->subtitle(); ?></p>
                    <?php endif; ?>
                    <div class="page-meta">
                        <time datetime="<?php $this->date('c'); ?>" class="page-date">
                            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <circle cx="12" cy="12" r="10"></circle>
                                <polyline points="12,6 12,12 16,14"></polyline>
                            </svg>
                            最后更新：<?php $this->date('Y-m-d'); ?>
                        </time>
                    </div>
                </header>

                <!-- Page Content -->
                <article class="page-content">
                    <div class="page-body">
                        <?php $this->content(); ?>
                    </div>

                    <!-- Friends Links Section -->
                    <div class="friends-section">
                        <h2 class="friends-title">
                            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                                <circle cx="9" cy="7" r="4"></circle>
                                <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                                <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                            </svg>
                            友情链接
                        </h2>
                        
                        <div class="friends-grid">
                            <?php 
                            // 友链数据 - 可以从自定义字段或数据库获取
                            $friends = array(
                                array(
                                    'name' => 'Typecho官网',
                                    'url' => 'https://typecho.org',
                                    'description' => 'Typecho官方网站',
                                    'avatar' => 'https://typecho.org/favicon.ico',
                                    'status' => 'active'
                                ),
                                array(
                                    'name' => 'GitHub',
                                    'url' => 'https://github.com',
                                    'description' => '全球最大的代码托管平台',
                                    'avatar' => 'https://github.com/favicon.ico',
                                    'status' => 'active'
                                ),
                                // 可以添加更多友链
                            );
                            
                            // 如果有自定义字段中的友链数据，优先使用
                            if ($this->fields->friends) {
                                $customFriends = json_decode($this->fields->friends, true);
                                if ($customFriends && is_array($customFriends)) {
                                    $friends = $customFriends;
                                }
                            }
                            
                            foreach ($friends as $friend): 
                            ?>
                            <div class="friend-card <?php echo $friend['status']; ?>">
                                <div class="friend-avatar">
                                    <img src="<?php echo $friend['avatar']; ?>" 
                                         alt="<?php echo $friend['name']; ?>" 
                                         loading="lazy"
                                         onerror="this.src='<?php $this->options->themeUrl('assets/images/default-avatar.png'); ?>'">
                                </div>
                                <div class="friend-info">
                                    <h3 class="friend-name">
                                        <a href="<?php echo $friend['url']; ?>" 
                                           target="_blank" 
                                           rel="noopener noreferrer"
                                           title="访问 <?php echo $friend['name']; ?>">
                                            <?php echo $friend['name']; ?>
                                        </a>
                                    </h3>
                                    <p class="friend-description">
                                        <?php echo $friend['description']; ?>
                                    </p>
                                    <div class="friend-meta">
                                        <span class="friend-url"><?php echo parse_url($friend['url'], PHP_URL_HOST); ?></span>
                                        <?php if (isset($friend['status']) && $friend['status'] === 'inactive'): ?>
                                            <span class="friend-status inactive">失效</span>
                                        <?php else: ?>
                                            <span class="friend-status active">正常</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="friend-actions">
                                    <a href="<?php echo $friend['url']; ?>" 
                                       target="_blank" 
                                       rel="noopener noreferrer"
                                       class="visit-btn"
                                       title="访问网站">
                                        <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                                            <polyline points="15,3 21,3 21,9"></polyline>
                                            <line x1="10" y1="14" x2="21" y2="3"></line>
                                        </svg>
                                    </a>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <!-- Apply for Friend Link -->
                    <div class="apply-section">
                        <h2 class="apply-title">
                            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
                                <rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect>
                            </svg>
                            申请友链
                        </h2>
                        
                        <div class="apply-content">
                            <div class="apply-info">
                                <h3>申请要求</h3>
                                <ul class="requirements-list">
                                    <li>网站内容健康，无违法违规信息</li>
                                    <li>网站能够正常访问，加载速度良好</li>
                                    <li>网站有一定的原创内容</li>
                                    <li>优先考虑技术类、设计类博客</li>
                                </ul>
                                
                                <h3>本站信息</h3>
                                <div class="site-info">
                                    <div class="info-item">
                                        <strong>网站名称：</strong><?php $this->options->title(); ?>
                                    </div>
                                    <div class="info-item">
                                        <strong>网站地址：</strong><?php $this->options->siteUrl(); ?>
                                    </div>
                                    <div class="info-item">
                                        <strong>网站描述：</strong><?php $this->options->description(); ?>
                                    </div>
                                    <div class="info-item">
                                        <strong>站长邮箱：</strong><?php $this->author->mail(); ?>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="apply-form-container">
                                <h3>申请表单</h3>
                                <form class="apply-form" id="friendLinkForm">
                                    <div class="form-group">
                                        <label for="siteName" class="form-label required">网站名称</label>
                                        <input type="text" id="siteName" name="siteName" class="form-input" required>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="siteUrl" class="form-label required">网站地址</label>
                                        <input type="url" id="siteUrl" name="siteUrl" class="form-input" required>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="siteDesc" class="form-label required">网站描述</label>
                                        <input type="text" id="siteDesc" name="siteDesc" class="form-input" required>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="siteAvatar" class="form-label">网站头像</label>
                                        <input type="url" id="siteAvatar" name="siteAvatar" class="form-input" placeholder="头像图片链接（可选）">
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="contactEmail" class="form-label required">联系邮箱</label>
                                        <input type="email" id="contactEmail" name="contactEmail" class="form-input" required>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="message" class="form-label">留言</label>
                                        <textarea id="message" name="message" class="form-textarea" rows="4" placeholder="介绍一下您的网站..."></textarea>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary">
                                        <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                                            <polyline points="22,6 12,13 2,6"></polyline>
                                        </svg>
                                        提交申请
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </article>

                <!-- Comments -->
                <?php $this->need('comments.php'); ?>
            </div>
            
            <!-- Sidebar -->
            <?php $this->need('sidebar.php'); ?>
        </div>
    </div>
</main>

<script>
// Friend Link Application Form
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('friendLinkForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);
            
            // 这里可以发送到后端处理，或者发送邮件
            // 示例：发送邮件
            const subject = '友链申请 - ' + data.siteName;
            const body = `
网站名称：${data.siteName}
网站地址：${data.siteUrl}
网站描述：${data.siteDesc}
网站头像：${data.siteAvatar || '未提供'}
联系邮箱：${data.contactEmail}
留言：${data.message || '无'}
            `.trim();
            
            const mailtoLink = `mailto:<?php $this->author->mail(); ?>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
            window.location.href = mailtoLink;
            
            // 显示提示信息
            alert('申请已提交，请等待审核。我们会通过邮件与您联系。');
        });
    }
});
</script>

<?php $this->need('footer.php'); ?>
