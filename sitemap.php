<?php
/**
 * XML Sitemap Generator for Typecho
 * 
 * @package Modern Responsive Theme
 */

if (!defined('__TYPECHO_ROOT_DIR__')) exit;

// Include Typecho
include_once 'config.inc.php';

// Set content type
header('Content-Type: application/xml; charset=utf-8');

// Get database and options
$db = Typecho_Db::get();
$options = Typecho_Widget::widget('Widget_Options');

// Start XML
echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:news="http://www.google.com/schemas/sitemap-news/0.9" xmlns:xhtml="http://www.w3.org/1999/xhtml" xmlns:mobile="http://www.google.com/schemas/sitemap-mobile/1.0" xmlns:image="http://www.google.com/schemas/sitemap-image/1.1" xmlns:video="http://www.google.com/schemas/sitemap-video/1.1">' . "\n";

// Homepage
echo '<url>' . "\n";
echo '<loc>' . $options->siteUrl . '</loc>' . "\n";
echo '<lastmod>' . date('c') . '</lastmod>' . "\n";
echo '<changefreq>daily</changefreq>' . "\n";
echo '<priority>1.0</priority>' . "\n";
echo '</url>' . "\n";

// Posts
$posts = $db->fetchAll($db->select()->from('table.contents')
    ->where('table.contents.status = ?', 'publish')
    ->where('table.contents.type = ?', 'post')
    ->order('table.contents.created', Typecho_Db::SORT_DESC));

foreach ($posts as $post) {
    $postUrl = Typecho_Router::url('post', $post, $options->index);
    
    echo '<url>' . "\n";
    echo '<loc>' . $postUrl . '</loc>' . "\n";
    echo '<lastmod>' . date('c', $post['modified']) . '</lastmod>' . "\n";
    echo '<changefreq>weekly</changefreq>' . "\n";
    echo '<priority>0.8</priority>' . "\n";
    
    // Add mobile annotation
    echo '<mobile:mobile/>' . "\n";
    
    // Add images if any
    if (preg_match_all('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $post['text'], $matches)) {
        foreach ($matches[1] as $imageUrl) {
            // Make sure URL is absolute
            if (strpos($imageUrl, 'http') !== 0) {
                $imageUrl = $options->siteUrl . ltrim($imageUrl, '/');
            }
            
            echo '<image:image>' . "\n";
            echo '<image:loc>' . htmlspecialchars($imageUrl) . '</image:loc>' . "\n";
            echo '<image:title>' . htmlspecialchars($post['title']) . '</image:title>' . "\n";
            echo '</image:image>' . "\n";
        }
    }
    
    // Add news annotation for recent posts (last 2 days)
    if ($post['created'] > (time() - 172800)) {
        echo '<news:news>' . "\n";
        echo '<news:publication>' . "\n";
        echo '<news:name>' . htmlspecialchars($options->title) . '</news:name>' . "\n";
        echo '<news:language>zh-cn</news:language>' . "\n";
        echo '</news:publication>' . "\n";
        echo '<news:publication_date>' . date('c', $post['created']) . '</news:publication_date>' . "\n";
        echo '<news:title>' . htmlspecialchars($post['title']) . '</news:title>' . "\n";
        echo '</news:news>' . "\n";
    }
    
    echo '</url>' . "\n";
}

// Pages
$pages = $db->fetchAll($db->select()->from('table.contents')
    ->where('table.contents.status = ?', 'publish')
    ->where('table.contents.type = ?', 'page'));

foreach ($pages as $page) {
    $pageUrl = Typecho_Router::url('page', $page, $options->index);
    
    echo '<url>' . "\n";
    echo '<loc>' . $pageUrl . '</loc>' . "\n";
    echo '<lastmod>' . date('c', $page['modified']) . '</lastmod>' . "\n";
    echo '<changefreq>monthly</changefreq>' . "\n";
    echo '<priority>0.6</priority>' . "\n";
    echo '<mobile:mobile/>' . "\n";
    echo '</url>' . "\n";
}

// Categories
$categories = $db->fetchAll($db->select()->from('table.metas')
    ->where('table.metas.type = ?', 'category'));

foreach ($categories as $category) {
    $categoryUrl = Typecho_Router::url('category', $category, $options->index);
    
    echo '<url>' . "\n";
    echo '<loc>' . $categoryUrl . '</loc>' . "\n";
    echo '<changefreq>weekly</changefreq>' . "\n";
    echo '<priority>0.5</priority>' . "\n";
    echo '<mobile:mobile/>' . "\n";
    echo '</url>' . "\n";
}

// Tags
$tags = $db->fetchAll($db->select()->from('table.metas')
    ->where('table.metas.type = ?', 'tag')
    ->limit(100)); // Limit tags to avoid too many URLs

foreach ($tags as $tag) {
    $tagUrl = Typecho_Router::url('tag', $tag, $options->index);
    
    echo '<url>' . "\n";
    echo '<loc>' . $tagUrl . '</loc>' . "\n";
    echo '<changefreq>weekly</changefreq>' . "\n";
    echo '<priority>0.4</priority>' . "\n";
    echo '<mobile:mobile/>' . "\n";
    echo '</url>' . "\n";
}

// Archive pages (yearly)
$archives = $db->fetchAll($db->select('FROM_UNIXTIME(created, "%Y") as year')
    ->from('table.contents')
    ->where('table.contents.status = ?', 'publish')
    ->where('table.contents.type = ?', 'post')
    ->group('year')
    ->order('year', Typecho_Db::SORT_DESC));

foreach ($archives as $archive) {
    $archiveUrl = $options->siteUrl . $archive['year'] . '/';
    
    echo '<url>' . "\n";
    echo '<loc>' . $archiveUrl . '</loc>' . "\n";
    echo '<changefreq>yearly</changefreq>' . "\n";
    echo '<priority>0.3</priority>' . "\n";
    echo '<mobile:mobile/>' . "\n";
    echo '</url>' . "\n";
}

// Special pages
$specialPages = array(
    'archives.html' => array('changefreq' => 'daily', 'priority' => '0.7'),
    'links.html' => array('changefreq' => 'monthly', 'priority' => '0.5'),
    'about.html' => array('changefreq' => 'monthly', 'priority' => '0.5')
);

foreach ($specialPages as $page => $config) {
    echo '<url>' . "\n";
    echo '<loc>' . $options->siteUrl . $page . '</loc>' . "\n";
    echo '<changefreq>' . $config['changefreq'] . '</changefreq>' . "\n";
    echo '<priority>' . $config['priority'] . '</priority>' . "\n";
    echo '<mobile:mobile/>' . "\n";
    echo '</url>' . "\n";
}

echo '</urlset>';
?>
