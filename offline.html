<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>离线模式 - Modern Typecho Blog</title>
    <style>
        :root {
            --color-primary: #2563eb;
            --color-accent: #3b82f6;
            --color-bg-primary: #ffffff;
            --color-bg-secondary: #f8fafc;
            --color-text-primary: #1e293b;
            --color-text-secondary: #64748b;
            --color-border: #e2e8f0;
            --spacing-4: 1rem;
            --spacing-6: 1.5rem;
            --spacing-8: 2rem;
            --radius-lg: 0.5rem;
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
            background-color: var(--color-bg-secondary);
            color: var(--color-text-primary);
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-4);
        }

        .offline-container {
            max-width: 600px;
            text-align: center;
            background-color: var(--color-bg-primary);
            padding: var(--spacing-8);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
        }

        .offline-icon {
            width: 120px;
            height: 120px;
            margin: 0 auto var(--spacing-6);
            color: var(--color-primary);
        }

        .offline-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: var(--spacing-4);
            color: var(--color-text-primary);
        }

        .offline-message {
            font-size: 1.125rem;
            color: var(--color-text-secondary);
            margin-bottom: var(--spacing-8);
            line-height: 1.7;
        }

        .offline-actions {
            display: flex;
            gap: var(--spacing-4);
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--radius-lg);
            font-size: 1rem;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background-color: var(--color-primary);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--color-accent);
            transform: translateY(-1px);
        }

        .btn-secondary {
            background-color: var(--color-bg-secondary);
            color: var(--color-text-primary);
            border: 1px solid var(--color-border);
        }

        .btn-secondary:hover {
            background-color: var(--color-border);
            transform: translateY(-1px);
        }

        .icon {
            width: 1rem;
            height: 1rem;
            stroke-width: 2;
        }

        .cached-pages {
            margin-top: var(--spacing-8);
            text-align: left;
        }

        .cached-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: var(--spacing-4);
            text-align: center;
        }

        .cached-list {
            list-style: none;
            display: grid;
            gap: 0.5rem;
        }

        .cached-item {
            padding: 0.75rem;
            background-color: var(--color-bg-secondary);
            border-radius: var(--radius-lg);
            border: 1px solid var(--color-border);
        }

        .cached-link {
            color: var(--color-primary);
            text-decoration: none;
            font-weight: 500;
        }

        .cached-link:hover {
            text-decoration: underline;
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            margin-top: var(--spacing-6);
            padding: 0.75rem;
            background-color: var(--color-bg-secondary);
            border-radius: var(--radius-lg);
            border: 1px solid var(--color-border);
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #ef4444;
            animation: pulse 2s infinite;
        }

        .status-dot.online {
            background-color: #10b981;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        @media (max-width: 640px) {
            .offline-container {
                padding: var(--spacing-6);
            }

            .offline-title {
                font-size: 1.5rem;
            }

            .offline-actions {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <!-- Offline Icon -->
        <div class="offline-icon">
            <svg viewBox="0 0 120 120" fill="none" stroke="currentColor">
                <!-- Cloud with X -->
                <path d="M30 80 Q30 65 45 65 Q50 50 70 50 Q90 50 95 65 Q110 65 110 80 Q110 95 95 95 L45 95 Q30 95 30 80 Z" stroke-width="3"/>
                <line x1="50" y1="70" x2="90" y2="70" stroke-width="4" stroke-linecap="round"/>
                <line x1="70" y1="50" x2="70" y2="90" stroke-width="4" stroke-linecap="round"/>
                
                <!-- WiFi symbol with X -->
                <path d="M20 100 Q60 60 100 100" stroke-width="2" opacity="0.3"/>
                <path d="M30 100 Q60 70 90 100" stroke-width="2" opacity="0.5"/>
                <path d="M40 100 Q60 80 80 100" stroke-width="2" opacity="0.7"/>
                <circle cx="60" cy="100" r="3" fill="currentColor"/>
                
                <!-- X mark -->
                <line x1="45" y1="45" x2="75" y2="75" stroke-width="4" stroke-linecap="round" stroke="#ef4444"/>
                <line x1="75" y1="45" x2="45" y2="75" stroke-width="4" stroke-linecap="round" stroke="#ef4444"/>
            </svg>
        </div>

        <!-- Offline Message -->
        <h1 class="offline-title">您当前处于离线状态</h1>
        <p class="offline-message">
            无法连接到互联网，但您仍然可以浏览已缓存的页面。<br>
            请检查您的网络连接，然后重试。
        </p>

        <!-- Actions -->
        <div class="offline-actions">
            <button onclick="window.location.reload()" class="btn btn-primary">
                <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <polyline points="23,4 23,10 17,10"></polyline>
                    <path d="M20.49,15a9,9,0,1,1-2.12-9.36L23,10"></path>
                </svg>
                重新加载
            </button>
            <a href="/" class="btn btn-secondary">
                <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                    <polyline points="9,22 9,12 15,12 15,22"></polyline>
                </svg>
                返回首页
            </a>
        </div>

        <!-- Connection Status -->
        <div class="status-indicator">
            <div class="status-dot" id="statusDot"></div>
            <span id="statusText">检查网络连接中...</span>
        </div>

        <!-- Cached Pages -->
        <div class="cached-pages">
            <h2 class="cached-title">可用的离线页面</h2>
            <ul class="cached-list" id="cachedList">
                <li class="cached-item">
                    <a href="/" class="cached-link">首页</a>
                </li>
                <li class="cached-item">
                    <a href="/archives.html" class="cached-link">文章归档</a>
                </li>
                <!-- 动态加载缓存的页面 -->
            </ul>
        </div>
    </div>

    <script>
        // Check online status
        function updateOnlineStatus() {
            const statusDot = document.getElementById('statusDot');
            const statusText = document.getElementById('statusText');
            
            if (navigator.onLine) {
                statusDot.classList.add('online');
                statusText.textContent = '网络连接已恢复';
                
                // Auto reload after 2 seconds
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                statusDot.classList.remove('online');
                statusText.textContent = '网络连接断开';
            }
        }

        // Listen for online/offline events
        window.addEventListener('online', updateOnlineStatus);
        window.addEventListener('offline', updateOnlineStatus);

        // Initial status check
        updateOnlineStatus();

        // Load cached pages from service worker
        async function loadCachedPages() {
            if ('caches' in window) {
                try {
                    const cacheNames = await caches.keys();
                    const cachedList = document.getElementById('cachedList');
                    const cachedUrls = new Set();

                    for (const cacheName of cacheNames) {
                        const cache = await caches.open(cacheName);
                        const requests = await cache.keys();
                        
                        for (const request of requests) {
                            const url = new URL(request.url);
                            if (url.pathname !== '/offline.html' && 
                                url.pathname !== '/sw.js' &&
                                !url.pathname.includes('/assets/')) {
                                cachedUrls.add({
                                    url: url.pathname,
                                    title: url.pathname === '/' ? '首页' : url.pathname
                                });
                            }
                        }
                    }

                    // Clear existing items except default ones
                    const defaultItems = cachedList.querySelectorAll('.cached-item');
                    
                    // Add cached pages
                    cachedUrls.forEach(page => {
                        // Skip if already exists
                        const exists = Array.from(defaultItems).some(item => 
                            item.querySelector('a').href.includes(page.url)
                        );
                        
                        if (!exists) {
                            const li = document.createElement('li');
                            li.className = 'cached-item';
                            li.innerHTML = `<a href="${page.url}" class="cached-link">${page.title}</a>`;
                            cachedList.appendChild(li);
                        }
                    });
                } catch (error) {
                    console.log('Failed to load cached pages:', error);
                }
            }
        }

        // Load cached pages on page load
        document.addEventListener('DOMContentLoaded', loadCachedPages);

        // Retry connection every 30 seconds
        setInterval(() => {
            if (!navigator.onLine) {
                // Try to fetch a small resource to test connection
                fetch('/favicon.ico', { 
                    method: 'HEAD',
                    cache: 'no-cache'
                }).then(() => {
                    // Connection restored
                    updateOnlineStatus();
                }).catch(() => {
                    // Still offline
                });
            }
        }, 30000);
    </script>
</body>
</html>
