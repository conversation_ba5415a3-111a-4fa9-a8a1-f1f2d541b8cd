<?php
/**
 * Modern Responsive Typecho Theme
 * 
 * @package ModernBlog
 * <AUTHOR> Name
 * @version 1.0.0
 * @link https://your-website.com
 */

if (!defined('__TYPECHO_ROOT_DIR__')) exit;
$this->need('header.php');
?>

<main class="main-content" role="main">
    <div class="container">
        <div class="content-wrapper">
            <!-- 主内容区域 -->
            <section class="posts-section">
                <?php if ($this->have()): ?>
                    <!-- 特色文章区域 -->
                    <?php if ($this->is('index') && $this->options->featuredPosts): ?>
                        <div class="featured-posts">
                            <h2 class="section-title"><?php _e('特色文章'); ?></h2>
                            <div class="featured-grid">
                                <!-- 特色文章将通过JavaScript动态加载 -->
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- 文章列表 -->
                    <div class="posts-container" data-layout="<?php echo $this->options->homeLayout ? $this->options->homeLayout : 'grid'; ?>">
                        <?php while($this->next()): ?>
                            <article class="post-card" itemscope itemtype="http://schema.org/BlogPosting">
                                <!-- 文章缩略图 -->
                                <?php if ($this->fields->thumbnail || $this->options->defaultThumbnail): ?>
                                    <div class="post-thumbnail">
                                        <a href="<?php $this->permalink() ?>" aria-label="<?php $this->title() ?>">
                                            <img src="<?php echo $this->fields->thumbnail ? $this->fields->thumbnail : $this->options->defaultThumbnail; ?>" 
                                                 alt="<?php $this->title() ?>" 
                                                 loading="lazy"
                                                 class="thumbnail-image">
                                        </a>
                                        <?php if ($this->sticky): ?>
                                            <span class="post-sticky" aria-label="<?php _e('置顶文章'); ?>">
                                                <svg class="icon" viewBox="0 0 24 24">
                                                    <path d="M16,12V4H17V2H7V4H8V12L6,14V16H11.2V22H12.8V16H18V14L16,12Z" />
                                                </svg>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>

                                <!-- 文章内容 -->
                                <div class="post-content">
                                    <!-- 文章元信息 -->
                                    <div class="post-meta">
                                        <time class="post-date" datetime="<?php $this->date('c'); ?>" itemprop="datePublished">
                                            <?php $this->date('Y-m-d'); ?>
                                        </time>
                                        <span class="meta-separator">·</span>
                                        <span class="post-category">
                                            <?php $this->category(','); ?>
                                        </span>
                                        <?php if ($this->commentsNum > 0): ?>
                                            <span class="meta-separator">·</span>
                                            <span class="post-comments">
                                                <svg class="icon" viewBox="0 0 24 24">
                                                    <path d="M9,22A1,1 0 0,1 8,21V18H4A2,2 0 0,1 2,16V4C2,2.89 2.9,2 4,2H20A2,2 0 0,1 22,4V16A2,2 0 0,1 20,18H13.9L10.2,21.71C10,21.9 9.75,22 9.5,22V22H9Z" />
                                                </svg>
                                                <?php $this->commentsNum('%d'); ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>

                                    <!-- 文章标题 -->
                                    <h2 class="post-title" itemprop="headline">
                                        <a href="<?php $this->permalink() ?>" itemprop="url">
                                            <?php $this->title() ?>
                                        </a>
                                    </h2>

                                    <!-- 文章摘要 -->
                                    <div class="post-excerpt" itemprop="description">
                                        <?php $this->excerpt(150, '...'); ?>
                                    </div>

                                    <!-- 文章标签 -->
                                    <?php if ($this->tags): ?>
                                        <div class="post-tags">
                                            <?php $this->tags(' ', true, 'none'); ?>
                                        </div>
                                    <?php endif; ?>

                                    <!-- 阅读更多 -->
                                    <div class="post-footer">
                                        <a href="<?php $this->permalink() ?>" class="read-more-btn">
                                            <?php _e('阅读全文'); ?>
                                            <svg class="icon" viewBox="0 0 24 24">
                                                <path d="M4,11V13H16L10.5,18.5L11.92,19.92L19.84,12L11.92,4.08L10.5,5.5L16,11H4Z" />
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </article>
                        <?php endwhile; ?>
                    </div>

                    <!-- 分页导航 -->
                    <nav class="pagination" role="navigation" aria-label="<?php _e('文章分页导航'); ?>">
                        <?php $this->pageNav('&laquo; 上一页', '下一页 &raquo;', 3, '...', array('wrapTag' => 'ul', 'wrapClass' => 'page-navigator', 'itemTag' => 'li', 'textTag' => 'a', 'currentClass' => 'current', 'prevClass' => 'prev', 'nextClass' => 'next')); ?>
                    </nav>

                <?php else: ?>
                    <!-- 无文章时的显示 -->
                    <div class="no-posts">
                        <div class="no-posts-icon">
                            <svg viewBox="0 0 24 24">
                                <path d="M19,3H5C3.9,3 3,3.9 3,5V19C3,20.1 3.9,21 5,21H19C20.1,21 21,20.1 21,19V5C21,3.9 20.1,3 19,3M19,19H5V5H19V19Z" />
                            </svg>
                        </div>
                        <h2><?php _e('暂无文章'); ?></h2>
                        <p><?php _e('这里还没有发布任何文章，请稍后再来查看。'); ?></p>
                    </div>
                <?php endif; ?>
            </section>

            <!-- 侧边栏 -->
            <?php $this->need('sidebar.php'); ?>
        </div>
    </div>
</main>

<?php $this->need('footer.php'); ?>
