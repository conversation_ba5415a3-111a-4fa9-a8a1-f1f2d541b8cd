<?php if (!defined('__TYPECHO_ROOT_DIR__')) exit; ?>
<!DOCTYPE html>
<html lang="<?php $this->options->lang(); ?>" <?php if ($this->options->darkMode): ?>data-theme="auto"<?php endif; ?>>
<head>
    <meta charset="<?php $this->options->charset(); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0">
    <meta name="format-detection" content="telephone=no">
    
    <!-- SEO Meta Tags -->
    <title><?php $this->archiveTitle(array(
        'category'  =>  _t('分类 %s 下的文章'),
        'search'    =>  _t('包含关键字 %s 的文章'),
        'tag'       =>  _t('标签 %s 下的文章'),
        'author'    =>  _t('%s 发布的文章')
    ), '', ' - '); ?><?php $this->options->title(); ?></title>
    
    <meta name="description" content="<?php if ($this->is('single')): ?><?php $this->excerpt(160, ''); ?><?php else: ?><?php $this->options->description(); ?><?php endif; ?>">
    <meta name="keywords" content="<?php if ($this->is('single')): ?><?php $this->tags(',', false); ?><?php else: ?><?php $this->options->keywords(); ?><?php endif; ?>">
    <meta name="author" content="<?php if ($this->is('single')): ?><?php $this->author(); ?><?php else: ?><?php $this->options->title(); ?><?php endif; ?>">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="<?php if ($this->is('single')): ?>article<?php else: ?>website<?php endif; ?>">
    <meta property="og:url" content="<?php $this->permalink(); ?>">
    <meta property="og:title" content="<?php if ($this->is('single')): ?><?php $this->title(); ?><?php else: ?><?php $this->options->title(); ?><?php endif; ?>">
    <meta property="og:description" content="<?php if ($this->is('single')): ?><?php $this->excerpt(160, ''); ?><?php else: ?><?php $this->options->description(); ?><?php endif; ?>">
    <meta property="og:image" content="<?php if ($this->is('single') && $this->fields->thumbnail): ?><?php echo $this->fields->thumbnail; ?><?php else: ?><?php echo $this->options->siteImage ? $this->options->siteImage : $this->options->themeUrl('assets/images/default-og.jpg'); ?><?php endif; ?>">
    <meta property="og:site_name" content="<?php $this->options->title(); ?>">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="<?php $this->permalink(); ?>">
    <meta property="twitter:title" content="<?php if ($this->is('single')): ?><?php $this->title(); ?><?php else: ?><?php $this->options->title(); ?><?php endif; ?>">
    <meta property="twitter:description" content="<?php if ($this->is('single')): ?><?php $this->excerpt(160, ''); ?><?php else: ?><?php $this->options->description(); ?><?php endif; ?>">
    <meta property="twitter:image" content="<?php if ($this->is('single') && $this->fields->thumbnail): ?><?php echo $this->fields->thumbnail; ?><?php else: ?><?php echo $this->options->siteImage ? $this->options->siteImage : $this->options->themeUrl('assets/images/default-og.jpg'); ?><?php endif; ?>">
    
    <!-- Canonical URL -->
    <link rel="canonical" href="<?php $this->permalink(); ?>">
    
    <!-- RSS Feed -->
    <link rel="alternate" type="application/rss+xml" title="<?php $this->options->title(); ?>" href="<?php $this->options->feedUrl(); ?>" />
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo $this->options->favicon ? $this->options->favicon : $this->options->themeUrl('assets/images/favicon.ico'); ?>">
    <link rel="apple-touch-icon" href="<?php echo $this->options->appleTouchIcon ? $this->options->appleTouchIcon : $this->options->themeUrl('assets/images/apple-touch-icon.png'); ?>">
    
    <!-- Preload Critical Resources -->
    <link rel="preload" href="<?php $this->options->themeUrl('assets/css/style.css'); ?>" as="style">
    <link rel="preload" href="<?php $this->options->themeUrl('assets/js/main.js'); ?>" as="script">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="<?php $this->options->themeUrl('assets/css/style.css'); ?>?v=<?php echo filemtime(dirname(__FILE__) . '/assets/css/style.css'); ?>">
    
    <!-- Theme Color -->
    <meta name="theme-color" content="<?php echo $this->options->themeColor ? $this->options->themeColor : '#2563eb'; ?>">
    <meta name="msapplication-TileColor" content="<?php echo $this->options->themeColor ? $this->options->themeColor : '#2563eb'; ?>">
    
    <!-- Structured Data -->
    <?php if ($this->is('single')): ?>
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "BlogPosting",
        "headline": "<?php $this->title(); ?>",
        "description": "<?php $this->excerpt(160, ''); ?>",
        "image": "<?php echo $this->fields->thumbnail ? $this->fields->thumbnail : $this->options->defaultThumbnail; ?>",
        "author": {
            "@type": "Person",
            "name": "<?php $this->author(); ?>"
        },
        "publisher": {
            "@type": "Organization",
            "name": "<?php $this->options->title(); ?>",
            "logo": {
                "@type": "ImageObject",
                "url": "<?php echo $this->options->siteLogo ? $this->options->siteLogo : $this->options->themeUrl('assets/images/logo.png'); ?>"
            }
        },
        "datePublished": "<?php $this->date('c'); ?>",
        "dateModified": "<?php echo date('c', $this->modified); ?>",
        "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": "<?php $this->permalink(); ?>"
        }
    }
    </script>
    <?php else: ?>
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "<?php $this->options->title(); ?>",
        "description": "<?php $this->options->description(); ?>",
        "url": "<?php $this->options->siteUrl(); ?>",
        "potentialAction": {
            "@type": "SearchAction",
            "target": "<?php $this->options->siteUrl(); ?>?s={search_term_string}",
            "query-input": "required name=search_term_string"
        }
    }
    </script>
    <?php endif; ?>
    
    <!-- Custom Head Code -->
    <?php if ($this->options->customHeadCode): ?>
        <?php echo $this->options->customHeadCode; ?>
    <?php endif; ?>
    
    <?php $this->header(); ?>
</head>
<body class="<?php if ($this->is('index')): ?>home<?php elseif ($this->is('post')): ?>single-post<?php elseif ($this->is('page')): ?>single-page<?php elseif ($this->is('category')): ?>category-archive<?php elseif ($this->is('tag')): ?>tag-archive<?php elseif ($this->is('author')): ?>author-archive<?php elseif ($this->is('search')): ?>search-results<?php elseif ($this->is('404')): ?>error-404<?php endif; ?>" itemscope itemtype="http://schema.org/WebPage">
    
    <!-- Skip to Content Link for Accessibility -->
    <a class="skip-link screen-reader-text" href="#main"><?php _e('跳转到主内容'); ?></a>
    
    <!-- Header -->
    <header class="site-header" role="banner" itemscope itemtype="http://schema.org/WPHeader">
        <div class="container">
            <div class="header-content">
                <!-- Logo and Site Title -->
                <div class="site-branding">
                    <a href="<?php $this->options->siteUrl(); ?>" class="site-logo" rel="home" itemprop="url">
                        <?php if ($this->options->siteLogo): ?>
                            <img src="<?php echo $this->options->siteLogo; ?>" alt="<?php $this->options->title(); ?>" class="logo-image">
                        <?php else: ?>
                            <span class="site-title" itemprop="name"><?php $this->options->title(); ?></span>
                        <?php endif; ?>
                    </a>
                    <?php if ($this->options->description): ?>
                        <p class="site-description" itemprop="description"><?php $this->options->description(); ?></p>
                    <?php endif; ?>
                </div>
                
                <!-- Navigation Menu -->
                <nav class="main-navigation" role="navigation" itemscope itemtype="http://schema.org/SiteNavigationElement">
                    <button class="menu-toggle" aria-controls="primary-menu" aria-expanded="false" aria-label="<?php _e('切换导航菜单'); ?>">
                        <span class="hamburger">
                            <span></span>
                            <span></span>
                            <span></span>
                        </span>
                    </button>
                    
                    <ul class="nav-menu" id="primary-menu">
                        <li class="<?php if ($this->is('index')): ?>current-menu-item<?php endif; ?>">
                            <a href="<?php $this->options->siteUrl(); ?>" itemprop="url">
                                <span itemprop="name"><?php _e('首页'); ?></span>
                            </a>
                        </li>
                        <?php $this->widget('Widget_Contents_Page_List')->to($pages); ?>
                        <?php while($pages->next()): ?>
                            <li class="<?php if ($this->is('page', $pages->slug)): ?>current-menu-item<?php endif; ?>">
                                <a href="<?php $pages->permalink(); ?>" itemprop="url">
                                    <span itemprop="name"><?php $pages->title(); ?></span>
                                </a>
                            </li>
                        <?php endwhile; ?>
                    </ul>
                </nav>
                
                <!-- Header Actions -->
                <div class="header-actions">
                    <!-- Search Toggle -->
                    <button class="search-toggle" aria-label="<?php _e('搜索'); ?>" aria-expanded="false">
                        <svg class="icon" viewBox="0 0 24 24">
                            <path d="M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z" />
                        </svg>
                    </button>
                    
                    <!-- Dark Mode Toggle -->
                    <button class="theme-toggle" aria-label="<?php _e('切换主题模式'); ?>" title="<?php _e('切换深色/浅色模式'); ?>">
                        <svg class="icon sun-icon" viewBox="0 0 24 24">
                            <path d="M12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18M12,2L14.39,5.42C13.65,5.15 12.84,5 12,5C11.16,5 10.35,5.15 9.61,5.42L12,2M3.34,7L7.5,6.65C6.9,7.16 6.36,7.78 5.94,8.5C5.5,9.24 5.25,10 5.11,10.79L3.34,7M3.36,17L5.12,13.23C5.26,14 5.53,14.78 5.95,15.5C6.37,16.24 6.91,16.86 7.5,17.37L3.36,17M20.65,7L18.88,10.79C18.74,10 18.47,9.23 18.05,8.5C17.63,7.78 17.1,7.15 16.5,6.64L20.65,7M20.64,17L16.5,17.36C17.09,16.85 17.62,16.22 18.04,15.5C18.46,14.77 18.73,14 18.87,13.21L20.64,17M12,22L9.59,18.56C10.33,18.83 11.14,19 12,19C12.82,19 13.63,18.83 14.37,18.56L12,22Z" />
                        </svg>
                        <svg class="icon moon-icon" viewBox="0 0 24 24">
                            <path d="M17.75,4.09L15.22,6.03L16.13,9.09L13.5,7.28L10.87,9.09L11.78,6.03L9.25,4.09L12.44,4L13.5,1L14.56,4L17.75,4.09M21.25,11L19.61,12.25L20.2,14.23L18.5,13.06L16.8,14.23L17.39,12.25L15.75,11L17.81,10.95L18.5,9L19.19,10.95L21.25,11M18.97,15.95C19.8,15.87 20.69,17.05 20.16,17.8C19.84,18.25 19.5,18.67 19.08,19.07C15.17,23 8.84,23 4.94,19.07C1.03,15.17 1.03,8.83 4.94,4.93C5.34,4.53 5.76,4.17 6.21,3.85C6.96,3.32 8.14,4.21 8.06,5.04C7.79,7.9 8.75,10.87 10.95,13.06C13.14,15.26 16.1,16.22 18.97,15.95M17.33,17.97C14.5,17.81 11.7,16.64 9.53,14.5C7.36,12.31 6.2,9.5 6.04,6.68C3.23,9.82 3.34,14.4 6.35,17.41C9.37,20.43 14,20.54 17.33,17.97Z" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Search Overlay -->
        <div class="search-overlay" aria-hidden="true">
            <div class="search-container">
                <form class="search-form" method="post" action="<?php $this->options->siteUrl(); ?>" role="search">
                    <label for="search-input" class="screen-reader-text"><?php _e('搜索'); ?></label>
                    <input type="search" id="search-input" name="s" placeholder="<?php _e('输入关键词搜索...'); ?>" autocomplete="off" required>
                    <button type="submit" class="search-submit" aria-label="<?php _e('提交搜索'); ?>">
                        <svg class="icon" viewBox="0 0 24 24">
                            <path d="M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z" />
                        </svg>
                    </button>
                </form>
                <button class="search-close" aria-label="<?php _e('关闭搜索'); ?>">
                    <svg class="icon" viewBox="0 0 24 24">
                        <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
                    </svg>
                </button>
            </div>
        </div>
    </header>
    
    <!-- Main Content Area -->
    <div id="main" class="site-main"><?php $this->need('functions.php'); ?>
